<template>
  <view class="citypicker">
    <!-- 使用uni-data-picker，按照官方文档的插槽语法 -->
    <uni-data-picker
      v-slot:default="{data, error, options}"
      v-model="selectedValue"
      :localdata="treeData"
      popup-title="选择城市"
      :clear="false"
      :border="false"
      @change="handleChange"
      class="citypicker-custom"
    >
      <view v-if="error" class="citypicker-error">
        <text>{{error}}</text>
      </view>
      <view v-else class="citypicker-display">
        <text class="citypicker-text" :class="{ 'citypicker-placeholder': !displayText }">
          {{ displayText || placeholder }}
        </text>
        <text class="citypicker-arrow">▼</text>
      </view>
    </uni-data-picker>
  </view>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import UniDataPicker from '@dcloudio/uni-ui/lib/uni-data-picker/uni-data-picker.vue'
import areaData from '@/utils/aiArea.json'

// Props
const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  placeholder: {
    type: String,
    default: '请选择城市'
  },
  // 显示的级别配置：['province', 'city', 'district']
  levels: {
    type: Array,
    default: () => ['province', 'city', 'district'],
    validator: (value) => {
      const validLevels = ['province', 'city', 'district']
      return value.every(level => validLevels.includes(level))
    }
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'change'])

// 响应式数据
const selectedValue = ref([])
const displayText = ref('')

// 计算属性 - 转换数据格式为uni-data-picker需要的树形结构
const treeData = computed(() => {
  return areaData.map(province => ({
    value: province.id,
    text: province.areaName,
    children: province.children?.map(city => ({
      value: city.id,
      text: city.areaName,
      children: city.children?.map(district => ({
        value: district.id,
        text: district.areaName
      })) || []
    })) || []
  }))
})

// 方法
const handleChange = (e) => {
  let selectedIds = e?.detail?.value ||[]

  // 确保我们有足够的数据（至少需要省份）
  if (selectedIds.length >= 1) {
    // 从对象数组中提取value值
    const provinceId = selectedIds[0]?.value
    const cityId = selectedIds[1]?.value
    const districtId = selectedIds[2]?.value

    // 查找对应的数据对象
    const province = areaData.find(p => p.id === provinceId)
    let city = null
    let district = null

    if (province && cityId) {
      city = province.children?.find(c => c.id === cityId)

      if (city && districtId) {
        district = city.children?.find(d => d.id === districtId)
      }
    }

    console.log('找到的数据对象:', { province, city, district })

    // 根据实际找到的数据生成显示文字和返回值
    if (province) {
      let textParts = []
      let resultIds = [provinceId]

      // 根据levels配置和实际数据生成显示文字
      if (props.levels.includes('province')) {
        textParts.push(province.areaName)
      }

      if (city) {
        resultIds.push(cityId)
        if (props.levels.includes('city')) {
          textParts.push(city.areaName)
        }
      }

      if (district) {
        resultIds.push(districtId)
        if (props.levels.includes('district')) {
          textParts.push(district.areaName)
        }
      }

      const newDisplayText = textParts.join(' ')
      displayText.value = newDisplayText

      // 只有当我们有完整的三级数据时才发送事件
      if (district) {
        emit('update:modelValue', resultIds)
        emit('change', {
          value: resultIds,
          text: displayText.value,
          province: province,
          city: city,
          district: district
        })
      } else {
        console.log('数据不完整，等待用户继续选择')
        console.log('当前状态:', {
          hasProvince: !!province,
          hasCity: !!city,
          hasDistrict: !!district,
          cityId,
          districtId,
          selectedIds
        })
      }
    } else {
      console.log('未找到省份数据')
    }
  } else {
    console.log('数据格式不正确:', e)
  }
}

// 初始化数据
const initData = () => {
  if (props.modelValue && Array.isArray(props.modelValue) && props.modelValue.length >= 1) {
    selectedValue.value = [...props.modelValue]
    console.log('设置selectedValue:', selectedValue.value)

    // 查找对应的数据对象来生成显示文字
    const provinceId = props.modelValue[0]
    const cityId = props.modelValue[1] || null
    const districtId = props.modelValue[2] || null

    const province = areaData.find(p => p.id === provinceId)
    let city = null
    let district = null

    if (province && cityId) {
      city = province.children?.find(c => c.id === cityId)

      if (city && districtId) {
        district = city.children?.find(d => d.id === districtId)
      }
    }

    console.log('初始化找到的数据:', { province, city, district })

    if (province) {
      let textParts = []

      if (props.levels.includes('province')) {
        textParts.push(province.areaName)
      }
      if (city && props.levels.includes('city')) {
        textParts.push(city.areaName)
      }
      if (district && props.levels.includes('district')) {
        textParts.push(district.areaName)
      }

      const newDisplayText = textParts.join(' ')
      displayText.value = newDisplayText
    }
  } else {
    selectedValue.value = []
    displayText.value = ''
    console.log('重置为空值')
  }
}

// 监听器
watch(() => props.modelValue, () => {
  initData()
}, { immediate: true })

onMounted(() => {
  initData()
})
</script>

<style scoped>
.citypicker {
  width: 100%;
  position: relative;
}

/* uni-data-picker自定义样式 */
.citypicker-custom {
  flex: 1;
}

/* 自定义触发器样式 */
.citypicker-display {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
  padding: 8rpx 20rpx 8rpx 0;
  min-height: 44rpx;
  background-color: transparent;
  border: none;
  cursor: pointer;
  position: relative;
  transition: opacity 0.2s ease;
}

.citypicker-display:active {
  opacity: 0.7;
}

.citypicker-text {
  font-size: 24rpx;
  color: #ffffff;
  text-align: right;
  padding-right: 8rpx;
}

.citypicker-placeholder {
  color: #8c8c8c;
}

.citypicker-arrow {
  font-size: 20rpx;
  color: #8c8c8c;
  margin-left: 8rpx;
  transition: transform 0.3s ease;
}

.citypicker-error {
  color: #ff0043;
  font-size: 24rpx;
  padding: 8rpx 20rpx 8rpx 0;
  text-align: right;
}

/* 覆盖uni-data-picker默认样式 */
/* 弹出层背景 */
:deep(.uni-data-tree-cover) {
  background-color: rgba(0, 0, 0, 0.8) !important;
}

/* 弹出层主体 */
:deep(.uni-data-tree-dialog) {
  background-color: #232325 !important;
  border-top-left-radius: 20rpx !important;
  border-top-right-radius: 20rpx !important;
}

/* 弹出层标题区域 */
:deep(.dialog-caption) {
  background-color: #212124 !important;
  border-bottom: 2rpx solid #353537 !important;
}

:deep(.dialog-title) {
  color: #ffffff !important;
}

:deep(.dialog-close-plus) {
  background-color: #ffffff !important;
}

/* 选择器主体 */
:deep(.uni-data-pickerview) {
  background-color: #232325 !important;
}

/* 顶部选项卡 */
:deep(.selected-list) {
  border-bottom: 1px solid #353537 !important;
  background-color: #212124 !important;
}

:deep(.selected-item) {
  color: #bfbfbf !important;
}

:deep(.selected-item text) {
  color: #bfbfbf !important;
}

:deep(.selected-item-active) {
  border-bottom: 2px solid #ff0043 !important;
}

:deep(.selected-item-active text) {
  color: #ff0043 !important;
  font-weight: 500 !important;
}

/* 列表项 */
:deep(.item) {
  border-bottom: 1px solid #353537 !important;
}

:deep(.item-text) {
  color: #ffffff !important;
}

/* 选中项的勾选标记 */
:deep(.check) {
  border-color: #ff0043 !important;
}

/* 美化滚动条 */
:deep(.list::-webkit-scrollbar) {
  width: 8rpx;
}

:deep(.list::-webkit-scrollbar-track) {
  background: transparent;
}

:deep(.list::-webkit-scrollbar-thumb) {
  background: #353537;
  border-radius: 4rpx;
}

:deep(.list::-webkit-scrollbar-thumb:hover) {
  background: #4a4a4c;
}
</style>
