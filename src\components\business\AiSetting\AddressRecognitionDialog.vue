<template>
  <!-- 地址识别弹窗 -->
  <view v-if="visible" class="address-dialog-overlay" @click="handleCancel">
    <view class="address-dialog" @click.stop>
      <view class="address-dialog-header">
        <text class="address-dialog-title">智能识别</text>
      </view>
      
      <view class="address-dialog-content">
        <view class="address-dialog-item">
          <text class="address-dialog-label">地区:</text>
          <text class="address-dialog-value">{{ addressData.region }}</text>
        </view>
        
        <view class="address-dialog-item">
          <text class="address-dialog-label">详细地址:</text>
          <text class="address-dialog-value">{{ addressData.detail }}</text>
        </view>
        
        <view class="address-dialog-note">
          <text class="address-dialog-note-text">注:地址中有系统自动补齐的内容,请核对后使用</text>
        </view>
      </view>
      
      <view class="address-dialog-footer">
        <button class="address-dialog-btn address-dialog-btn-cancel" @click="handleCancel">
          <text class="address-dialog-btn-text">取消</text>
        </button>
        <button class="address-dialog-btn address-dialog-btn-confirm" @click="handleConfirm">
          <text class="address-dialog-btn-text">确定使用</text>
        </button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'

// Props定义
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  addressData: {
    type: Object,
    default: () => ({ 
      region: '', 
      detail: '', 
      provinceCode: '', 
      cityCode: '', 
      areaCode: '' 
    })
  }
})

// 事件定义
const emit = defineEmits(['update:visible', 'confirm', 'cancel'])

// 取消操作
const handleCancel = () => {
  emit('update:visible', false)
  emit('cancel')
}

// 确认操作
const handleConfirm = () => {
  emit('confirm', props.addressData)
  emit('update:visible', false)
}
</script>

<style scoped>
/* 地址识别弹窗样式 */
.address-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.address-dialog {
  width: 90%;
  max-width: 600rpx;
  background-color: #111113;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
}

.address-dialog-header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 32rpx;
  background-color: #1d1d1e;
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 500;
}

.address-dialog-title {
  color: #ffffff;
}

.address-dialog-content {
  padding: 24rpx 32rpx;
  flex: 1;
  overflow-y: auto;
  color: #ffffff;
  font-size: 28rpx;
  background-color: #1d1d1e;
}

.address-dialog-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  gap: 20rpx;
}

.address-dialog-label {
  font-size: 28rpx;
  color: #bfbfbf;
  min-width: 150rpx;
}

.address-dialog-value {
  flex: 1;
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 500;
}

.address-dialog-note {
  margin-top: 20rpx;
  padding-top: 16rpx;
  color: #8c8c8c;
  font-size: 24rpx;
}

.address-dialog-note-text {
  color: #8c8c8c;
  font-size: 24rpx;
}

.address-dialog-footer {
  display: flex;
  justify-content: space-around;
  padding: 24rpx 32rpx;
  background-color: #1d1d1e;
}

.address-dialog-btn {
  width: 200rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 40rpx;
  font-size: 32rpx;
  font-weight: 500;
  cursor: pointer;
}

.address-dialog-btn-cancel {
  background-color: #383839;
  border: 2rpx solid #353537;
  color: #bfbfbf;
}

.address-dialog-btn-cancel:active {
  background-color: #ff0043;
  color: #ffffff;
}

.address-dialog-btn-confirm {
  background: #ff0043;
  border: none;
  color: #ffffff;
}

.address-dialog-btn-confirm:active {
  background: #e6003a;
}

.address-dialog-btn-text {
  color: #ffffff;
}
</style> 