<template>
  <view v-if="visible" class="age-range-modal" @click="handleMaskClick">
    <view class="age-range-container" @click.stop>
      <!-- 头部 -->
      <view class="age-range-header">
        <text class="age-range-title">{{ title }}</text>
        <view class="age-range-close-btn" @click="handleClose">
          <text class="age-range-close-icon">×</text>
        </view>
      </view>

      <!-- 内容区域 -->
      <view class="age-range-content">
        <view class="age-range-input-wrapper">
          <view class="age-range-input-group">
            <view class="age-range-input-box">
              <input
                class="age-range-input"
                type="number"
                placeholder="请填写"
                v-model="minAge"
                @input="handleMinAgeInput"
                ref="minAgeRef"
              />
              <text class="age-range-divider">|</text>
              <text class="age-range-unit">岁</text>
            </view>
            <text class="age-range-separator">-</text>
            <view class="age-range-input-box">
              <input
                class="age-range-input"
                type="number"
                placeholder="请填写"
                v-model="maxAge"
                @input="handleMaxAgeInput"
                ref="maxAgeRef"
              />
              <text class="age-range-divider">|</text>
              <text class="age-range-unit">岁</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 底部按钮 -->
      <view class="age-range-footer">
        <view class="age-range-btn age-range-btn-confirm" @click="handleConfirm">
          <text class="age-range-btn-text">确认</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, nextTick, watch } from 'vue'

// Props定义
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '请输入货品适合年龄段'
  },
  defaultValue: {
    type: String,
    default: ''
  }
})

// 事件定义
const emit = defineEmits(['update:visible', 'confirm'])

// 数据状态
const minAge = ref('')
const maxAge = ref('')
const minAgeRef = ref(null)
const maxAgeRef = ref(null)

// 监听visible变化，自动聚焦输入框
watch(() => props.visible, (newVal) => {
  if (newVal) {
    // 解析默认值
    if (props.defaultValue) {
      const ages = props.defaultValue.split('-')
      if (ages && Array.isArray(ages) && ages.length > 1) {
        minAge.value = ages[0].split('岁')[0]
        maxAge.value = ages[1].split('岁')[0]
      }
    } else {
      minAge.value = ''
      maxAge.value = ''
    }
    
    // 在uni-app中，输入框会自动聚焦，无需手动处理
  }
})

// 方法定义
const handleMinAgeInput = (e) => {
  const value = e.detail.value
  if (value && !isNaN(value)) {
    minAge.value = Math.abs(parseInt(value)).toString()
  } else {
    minAge.value = value
  }
}

const handleMaxAgeInput = (e) => {
  const value = e.detail.value
  if (value && !isNaN(value)) {
    maxAge.value = Math.abs(parseInt(value)).toString()
  } else {
    maxAge.value = value
  }
}

const handleConfirm = () => {
  // 验证输入
  if (!minAge.value && !maxAge.value) {
    handleClose()
    return
  }
  
  // 如果输入不完整，提示用户
  if ((minAge.value && !maxAge.value) || (!minAge.value && maxAge.value)) {
    uni.showToast({
      title: '请填写完整',
      icon: 'none',
      duration: 2000
    })
    return
  }
  
  if (minAge.value && maxAge.value) {
    const min = parseInt(minAge.value)
    const max = parseInt(maxAge.value)
    
    // 验证年龄范围 (0-99)
    if (min < 0 || min > 99 || max < 0 || max > 99) {
      uni.showToast({
        title: '请输入0-99的年龄范围',
        icon: 'none',
        duration: 2000
      })
      return
    }
    
    // 验证最小年龄不大于最大年龄
    if (min > max) {
      uni.showToast({
        title: '最小年龄不能大于最大年龄',
        icon: 'none',
        duration: 2000
      })
      return
    }
    
    const ageStr = `${minAge.value}岁-${maxAge.value}岁`
    emit('confirm', ageStr)
    handleClose()
  }
}

const handleClose = () => {
  minAge.value = ''
  maxAge.value = ''
  emit('update:visible', false)
}

const handleMaskClick = () => {
  handleClose()
}
</script>

<style scoped>
/* 年龄段弹窗样式 */
.age-range-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-out;
}

.age-range-container {
  width: 600rpx;
  background-color: #232325;
  border-radius: 24rpx;
  overflow: hidden;
  animation: scaleIn 0.3s ease-out;
}

/* 头部样式 */
.age-range-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 40rpx 24rpx 40rpx;
  border-bottom: 2rpx solid #353537;
}

.age-range-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #ffffff;
}

.age-range-close-btn {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.age-range-close-btn:active {
  background-color: #383839;
}

.age-range-close-icon {
  font-size: 36rpx;
  color: #8c8c8c;
  line-height: 1;
}

/* 内容区域 */
.age-range-content {
  padding: 40rpx;
}

.age-range-input-wrapper {
  position: relative;
}

.age-range-input-group {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20rpx;
}

.age-range-input-box {
  display: flex;
  align-items: center;
  background: #383839;
  border-radius: 16rpx;
  padding: 0 20rpx;
  height: 88rpx;
  min-width: 200rpx;
  transition: all 0.2s;
}

.age-range-input-box:focus-within {
  background: #2a2a2c;
}

.age-range-input {
  flex: 1;
  height: 100%;
  background: transparent;
  border: none;
  text-align: center;
  color: #ffffff;
  font-size: 28rpx;
  outline: none;
}

.age-range-input::placeholder {
  color: #8c8c8c;
  font-size: 28rpx;
  text-align: center;
}

.age-range-divider {
  color: #e5e5e5;
  margin: 0 16rpx;
  font-size: 24rpx;
}

.age-range-unit {
  color: #bfbfbf;
  font-size: 24rpx;
}

.age-range-separator {
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 500;
  margin: 0 10rpx;
}

/* 底部按钮 */
.age-range-footer {
  border-top: 2rpx solid #353537;
  padding: 20rpx 0;
}

.age-range-btn {
  height: 90rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  margin: 0 20rpx;
  border-radius: 8rpx;
}

.age-range-btn-confirm {
  background-color: #ff0043;
  width: calc(100% - 40rpx);
}

.age-range-btn-confirm:active {
  background-color: #e6003a;
}

.age-range-btn-text {
  font-size: 32rpx;
  font-weight: 500;
  color: #ffffff;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* 响应式适配 */
/* @media (max-width: 375px) {
  .age-range-container {
    width: 560rpx;
  }
  
  .age-range-header {
    padding: 24rpx 32rpx 20rpx 32rpx;
  }
  
  .age-range-content {
    padding: 32rpx;
  }
  
  .age-range-input-box {
    min-width: 160rpx;
  }
} */
</style> 