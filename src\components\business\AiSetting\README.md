# AiSetting 组件

AI设置组件，支持弹窗和页面两种显示模式，用于收集用户的店铺信息。

## 功能特性

- 🎯 **双模式支持**: 支持弹窗模式和页面模式
- 📱 **响应式设计**: 适配不同屏幕尺寸
- 🎨 **深色主题**: 基于深色主题的现代化UI设计
- 🔧 **高度可配置**: 支持多种配置选项
- 📋 **表单验证**: 内置完整的数据验证逻辑
- 🚀 **易于集成**: 简单的API设计
- 💾 **本地存储**: 自动保存和恢复用户设置
- ✏️ **动态编辑**: 支持长按编辑和添加自定义标签
- 📍 **地址管理**: 支持城市选择和地址粘贴功能
- 📏 **衣杆管理**: 支持多衣杆长度管理和统计
- 🏷️ **大类小类关系**: 支持大类小类关联关系的本地存储和管理
- 🎯 **智能选择**: 货品擅长客群支持单选模式
- 🌍 **城市选择器**: 集成自定义CityPicker组件，支持灵活的显示配置

## 基本用法

### 弹窗模式 (推荐)

```vue
<template>
  <view>
    <button @click="showAiSetting">打开AI设置</button>
    
    <AiSetting
      v-model:visible="aiSettingVisible"
      @save="handleSave"
      @close="handleClose"
    />
  </view>
</template>

<script setup>
import { ref } from 'vue'
import AiSetting from '@/components/AiSetting/index.vue'

const aiSettingVisible = ref(false)

const showAiSetting = () => {
  aiSettingVisible.value = true
}

const handleSave = (data) => {
  console.log('保存数据:', data)
  aiSettingVisible.value = false
}

const handleClose = () => {
  console.log('关闭弹窗')
}
</script>
```

## Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| visible | Boolean | false | 是否显示组件 |

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:visible | Boolean | 显示状态变化 |
| save | Object | 保存数据时触发 |
| close | - | 关闭组件时触发 |

## 数据结构

### 表单数据 (FormData)

```javascript
{
  shopLocationId: 1,                    // 本店定位 ID
  assistantIndustryIdList: [1, 2],      // 主营类型 ID 数组
  firstCategoryId: 1,                   // 经营类目（大类）ID
  secondCategoryIdList: [1, 2],         // 经营类目（小类）ID 数组
  goodsApplyAgeId: 1,                   // 货品适合年龄段 ID
  goodsClientFeatureIdList: [1],         // 货品擅长客群 ID 数组（单选）
  roleId: 1,                            // 角色 ID
  introduce: '店铺介绍...',              // 店铺介绍
  shopArea: '100',                      // 店铺面积
  comAddress: '详细地址',                // 详细地址
  cityArray: [440000, 440300, 440305],   // 城市ID数组 [省份ID, 城市ID, 区县ID]
  cityText: '深圳市 南山区',              // 城市文本（根据CityPicker配置显示）
  clothesLineLengthList: [               // 衣杆长度列表
    { lengthNum: '120' },
    { lengthNum: '150' }
  ],
  clothesLineTotal: 2,                  // 衣杆总数
  clothesLineLengthTotal: 270           // 衣杆长度总计
}
```

### 选项数据

```javascript
// 本店定位选项
shopLocations: [
  { id: 1, name: '线下实体店' },
  { id: 2, name: '线上店铺' },
  { id: 3, name: '线上线下都有' }
]

// 主营类型选项
assistantIndustrys: [
  { id: 1, name: '零售' },
  { id: 2, name: '一批' },
  { id: 3, name: '二批' },
  { id: 4, name: '连锁' }
]

// 经营类目选项
firstCategorys: [
  { id: 1, name: '女装', children: [] },
  { id: 2, name: '男装', children: [] },
  { id: 3, name: '童装', children: [] },
  { id: 4, name: '内衣', children: [] },
  { id: 5, name: '鞋包', children: [] },
  { id: 6, name: '配饰', children: [] }
]

// 年龄段选项
goodsApplyAges: [
  { id: 1, name: '0岁-3岁' },
  { id: 2, name: '4岁-12岁' },
  { id: 3, name: '13岁-18岁' },
  { id: 4, name: '19岁-25岁' },
  { id: 5, name: '26岁-35岁' },
  { id: 6, name: '36岁-45岁' },
  { id: 7, name: '46岁-60岁' },
  { id: 8, name: '60岁以上' }
]

// 客群特点选项
goodsClientFeatures: [
  { id: 1, name: '学生' },
  { id: 2, name: '上班族' },
  { id: 3, name: '宝妈' },
  { id: 4, name: '时尚达人' },
  { id: 5, name: '商务人士' },
  { id: 6, name: '居家人群' }
]

// 角色选项
roles: [
  { id: 1, name: '店主' },
  { id: 2, name: '店员' },
  { id: 3, name: '导购' },
  { id: 4, name: '搭配师' },
  { id: 5, name: '买手' }
]
```

## 高级功能

### 动态标签管理

组件支持动态添加和编辑标签：

- **长按编辑**: 长按任何标签项可以编辑其名称
- **添加新标签**: 点击"添加"按钮可以添加新的标签项
- **年龄段管理**: 支持自定义年龄段范围设置

### 衣杆管理

- **多衣杆支持**: 可以添加多个衣杆并设置不同长度
- **长度验证**: 衣杆长度限制在30-999CM之间
- **统计功能**: 自动计算衣杆总数和长度总计
- **展开/收起**: 支持衣杆列表的展开和收起

### 地址管理

- **城市选择**: 集成CityPicker组件，支持省市区三级联动选择
- **灵活显示**: 支持配置显示级别，如只显示"城市 区县"
- **完整数据**: 始终返回完整的省市区ID数据，便于后端处理
- **地址验证**: 确保至少填写城市或详细地址

### 大类小类关系管理

- **关系存储**: 支持大类小类关联关系的本地存储
- **动态加载**: 根据保存的关系动态加载小类选项
- **系统标签**: 自动初始化系统预设的大类小类关系
- **自定义标签**: 支持用户添加的自定义大类小类及其关系
- **数据同步**: 确保大类小类关系数据的一致性

### 智能选择模式

- **货品擅长客群**: 改为单选模式，用户只能选择一个客群特点
- **自动切换**: 选择新项时自动取消之前的选择
- **添加新项**: 添加新客群特点时自动选中

### 表单验证

组件内置完整的表单验证：

- **必填项验证**: 本店定位、主营类型、角色、店铺介绍
- **条件验证**: 根据主营类型显示/隐藏相关选项
- **数据格式验证**: 数字输入、长度限制等
- **实时验证**: 输入时实时验证数据格式

### 本地存储

- **自动保存**: 用户数据自动保存到本地存储
- **数据恢复**: 组件加载时自动恢复上次的设置
- **分离存储**: AI设置数据和自定义标签数据分别存储
- **关系存储**: 大类小类关系单独存储，支持复杂的关联关系
- **数据同步**: 确保数据的一致性和完整性

## 样式定制

组件使用了深色主题的颜色规范：

- 主色调：`#ff0043`
- 背景色：`#111113`, `#232325`, `#383839`
- 文本色：`#ffffff`, `#bfbfbf`, `#8c8c8c`
- 边框色：`#353537`

如需自定义样式，可以通过CSS变量或深度选择器进行覆盖。

## 技术实现

### 数据存储结构

组件使用多个本地存储键来管理不同类型的数据：

```javascript
// AI设置主数据
uni.getStorageSync('aiSettings')

// 自定义标签数据
uni.getStorageSync('customLabels')

// 大类小类关系数据
uni.getStorageSync('aiModalCategoryRelation')
```

### 弹窗显示监听

组件使用 `watch` 监听 `visible` 属性变化，而不是 `onShow`：

```javascript
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    loadAiSettings()
  }
})
```

### 大类小类关系管理

```javascript
// 初始化关系
const initCategoryRelation = () => {
  // 从本地存储加载关系
  let categoryRelationObj = getCategoryRelation()

  // 如果没有关系数据，初始化系统标签关系
  if (Object.keys(categoryRelationObj).length === 0) {
    // 根据parent关系初始化
  }

  // 设置children属性
  firstCategorys.value.forEach(item => {
    const secondCategoryIds = categoryRelationObj[item.id] || []
    item.children = secondCategorys.value.filter(child =>
      secondCategoryIds.includes(child.id)
    )
  })
}
```

## 注意事项

1. **权限处理**: 粘贴地址功能需要剪贴板权限
2. **平台兼容**: 支持iOS、Android、H5平台
3. **数据验证**: 组件内置了完整的数据验证逻辑
4. **本地存储**: 数据会自动保存到本地，无需手动处理
5. **响应式设计**: 适配不同屏幕尺寸，支持小屏设备
6. **弹窗监听**: 使用watch监听visible属性，确保弹窗显示时正确加载数据
7. **单选模式**: 货品擅长客群为单选模式，注意业务逻辑处理

## 子组件

### CityPicker 城市选择器

组件集成了自定义的CityPicker组件，详细文档请参考：[CityPicker.md](../CityPicker/CityPicker.md)

**主要特性**：
- 三级联动选择（省市区）
- 底部弹窗交互
- 灵活的显示文字配置
- 深色主题适配
- 完整的数据返回

**使用示例**：
```vue
<CityPicker
  v-model="formData.cityArray"
  :levels="['city', 'district']"
  placeholder="请选择城市"
  @change="onCityChange"
/>
```

## 更新日志

### v2.1.0
- 🆕 集成CityPicker组件，支持灵活的城市选择
- 🆕 新增大类小类关系管理功能
- 🔄 货品擅长客群改为单选模式
- 🔧 优化本地存储结构，支持分离存储
- 🐛 修复弹窗显示时的数据加载问题
- 📱 优化移动端交互体验

### v2.0.0
- 新增深色主题设计
- 完善表单验证逻辑
- 新增衣杆管理功能
- 新增动态标签编辑功能
- 优化本地存储机制
- 新增地址粘贴功能
- 完善响应式适配

### v1.0.0
- 初始版本发布
- 支持弹窗模式
- 基本的表单功能
- 简单的数据验证
