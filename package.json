{"name": "wangcut-app", "version": "1.0.0", "description": "旺剪App - 智能短视频剪辑与运营工具", "main": "main.js", "scripts": {"dev:custom": "uni -p", "dev:h5": "uni", "dev:h5:ssr": "uni --ssr", "dev:mp-alipay": "uni -p mp-alipay", "dev:mp-baidu": "uni -p mp-baidu", "dev:mp-jd": "uni -p mp-jd", "dev:mp-kuaishou": "uni -p mp-kua<PERSON>ou", "dev:mp-lark": "uni -p mp-lark", "dev:mp-qq": "uni -p mp-qq", "dev:mp-toutiao": "uni -p mp-to<PERSON><PERSON>", "dev:mp-harmony": "uni -p mp-harmony", "dev:mp-weixin": "uni -p mp-weixin", "dev:mp-xhs": "uni -p mp-xhs", "dev:quickapp-webview": "uni -p quickapp-webview", "dev:quickapp-webview-huawei": "uni -p quickapp-webview-huawei", "dev:quickapp-webview-union": "uni -p quickapp-webview-union", "build:custom": "uni build -p", "build:h5": "uni build", "build:h5:ssr": "uni build --ssr", "build:mp-alipay": "uni build -p mp-alipay", "build:mp-baidu": "uni build -p mp-baidu", "build:mp-jd": "uni build -p mp-jd", "build:mp-kuaishou": "uni build -p mp-kuaishou", "build:mp-lark": "uni build -p mp-lark", "build:mp-qq": "uni build -p mp-qq", "build:mp-toutiao": "uni build -p mp-to<PERSON>ao", "build:mp-harmony": "uni build -p mp-harmony", "build:mp-weixin": "uni build -p mp-weixin", "build:mp-xhs": "uni build -p mp-xhs", "build:quickapp-webview": "uni build -p quickapp-webview", "build:quickapp-webview-huawei": "uni build -p quickapp-webview-huawei", "build:quickapp-webview-union": "uni build -p quickapp-webview-union", "build:app": "uni build -p app-plus", "build:app-dev": "cross-env NODE_ENV=development uni build -p app-plus --mode development", "test:e2e:h5": "cross-env UNI_PLATFORM=h5 jest --config=jest.e2e.config.js", "test:e2e:android": "cross-env UNI_PLATFORM=app UNI_OS_NAME=android jest src/pages/tests/e2e --config=jest.config.js", "test:e2e:ios": "cross-env UNI_PLATFORM=app UNI_OS_NAME=ios jest src/pages/tests/e2e --config=jest.config.js", "test": "node src/tests/scripts/run-tests.js --unit"}, "keywords": ["uni-app", "vue3", "短视频", "AI剪辑", "视频编辑"], "author": "WangCut Team", "license": "MIT", "dependencies": {"@dcloudio/uni-app": "3.0.0-4070520250711001", "@dcloudio/uni-app-harmony": "3.0.0-4070520250711001", "@dcloudio/uni-app-plus": "3.0.0-4070520250711001", "@dcloudio/uni-components": "3.0.0-4070520250711001", "@dcloudio/uni-h5": "3.0.0-4070520250711001", "@dcloudio/uni-i18n": "2.0.2-4060620250520001", "@dcloudio/uni-mp-alipay": "3.0.0-4070520250711001", "@dcloudio/uni-mp-baidu": "3.0.0-4070520250711001", "@dcloudio/uni-mp-harmony": "3.0.0-4070520250711001", "@dcloudio/uni-mp-jd": "3.0.0-4070520250711001", "@dcloudio/uni-mp-kuaishou": "3.0.0-4070520250711001", "@dcloudio/uni-mp-lark": "3.0.0-4070520250711001", "@dcloudio/uni-mp-qq": "3.0.0-4070520250711001", "@dcloudio/uni-mp-toutiao": "3.0.0-4070520250711001", "@dcloudio/uni-mp-weixin": "3.0.0-4070520250711001", "@dcloudio/uni-mp-xhs": "3.0.0-4070520250711001", "@dcloudio/uni-quickapp-webview": "3.0.0-4070520250711001", "@dcloudio/uni-ui": "1.5.10", "@playwright/test": "1.15.2", "adbkit": "2.11.1", "axios": "1.10.0", "cross-env": "7.0.3", "dayjs": "1.11.13", "jest-image-snapshot": "6.1.0", "node-simctl": "7.7.5", "pinia": "2.3.1", "playwright": "1.15.2", "puppeteer": "10.4.0", "vue": "3.5.17", "vue-i18n": "9.1.9"}, "devDependencies": {"@dcloudio/types": "3.4.19", "@dcloudio/uni-automator": "3.0.0-4070520250711001", "@dcloudio/uni-cli-shared": "3.0.0-4070520250711001", "@dcloudio/uni-stacktracey": "3.0.0-4070520250711001", "@dcloudio/vite-plugin-uni": "3.0.0-4070520250711001", "@vue/eslint-config-typescript": "12.0.0", "@vue/runtime-core": "3.4.21", "@vue/test-utils": "2.4.6", "adbkit": "2.11.1", "eslint": "8.57.1", "eslint-plugin-vue": "9.33.0", "jest": "27.0.4", "jest-environment-node": "27.5.1", "jest-html-reporters": "3.1.7", "sass": "1.89.2", "typescript": "5.8.3", "vite": "5.2.8"}, "engines": {"node": ">=16.0.0"}, "overrides": {"rollup": "4.14.3"}}