<template>
  <view v-if="visible" class="add-label-modal" @click="handleMaskClick">
    <view class="add-label-container" @click.stop>
      <!-- 头部 -->
      <view class="add-label-header">
        <text class="add-label-title">{{ title }}</text>
        <view class="add-label-close-btn" @click="handleClose">
          <text class="add-label-close-icon">×</text>
        </view>
      </view>

      <!-- 内容区域 -->
      <view class="add-label-content">
        <view class="add-label-input-wrapper">
          <input
            class="add-label-input"
            type="text"
            :placeholder="placeholder"
            v-model="inputValue"
            maxlength="20"
            @input="handleInput"
            @confirm="handleConfirm"
            ref="inputRef"
          />

        </view>
      </view>

      <!-- 底部按钮 -->
      <view class="add-label-footer">
        <view class="add-label-btn add-label-btn-confirm" @click="handleConfirm">
          <text class="add-label-btn-text">确定</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, nextTick, watch } from 'vue'

// Props定义
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '添加标签'
  },
  placeholder: {
    type: String,
    default: '请输入新标签名称'
  },
  defaultValue: {
    type: String,
    default: ''
  }
})

// 事件定义
const emit = defineEmits(['update:visible', 'confirm', 'cancel'])

// 数据状态
const inputValue = ref('')
const inputRef = ref(null)

// 监听visible变化，自动聚焦输入框
watch(() => props.visible, (newVal) => {
  if (newVal) {
    inputValue.value = props.defaultValue
    // 在uni-app中，输入框会自动聚焦，无需手动处理
  }
})

// 方法定义
const handleInput = (e) => {
  inputValue.value = e.detail.value
}

const handleConfirm = () => {
  // 如果没有输入内容，直接关闭弹窗不做任何操作
  if (!inputValue.value.trim()) {
    handleClose()
    return
  }
  
  emit('confirm', inputValue.value.trim())
  handleClose()
}



const handleClose = () => {
  inputValue.value = ''
  emit('update:visible', false)
}

const handleMaskClick = () => {
  handleClose()
}
</script>

<style scoped>
/* 添加标签弹窗样式 */
.add-label-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-out;
}

.add-label-container {
  width: 600rpx;
  background-color: #232325;
  border-radius: 12rpx;
  overflow: hidden;
  animation: scaleIn 0.3s ease-out;
}

/* 头部样式 */
.add-label-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 40rpx 24rpx 40rpx;
  border-bottom: 2rpx solid #353537;
}

.add-label-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #ffffff;
}

.add-label-close-btn {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 8rpx;
  transition: background-color 0.2s;
}

.add-label-close-btn:active {
  background-color: #383839;
}

.add-label-close-icon {
  font-size: 36rpx;
  color: #8c8c8c;
  line-height: 1;
}

/* 内容区域 */
.add-label-content {
  padding: 40rpx;
}

.add-label-input-wrapper {
  position: relative;
}

.add-label-input {
  width: 100%;
  height: 88rpx;
  background: #383839;
  border-radius: 8rpx;
  padding: 0 32rpx;
  border: 2rpx solid transparent;
  font-size: 28rpx;
  color: #ffffff;
  outline: none;
  transition: all 0.2s;
}

.add-label-input:focus {
  border-color: #ff0043;
  background: #2a2a2c;
}

.add-label-input::placeholder {
  color: #8c8c8c;
  font-size: 28rpx;
}



/* 底部按钮 */
.add-label-footer {
  border-top: 2rpx solid #353537;
  padding: 20rpx 0;
}

.add-label-btn {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  margin: 0 20rpx;
  border-radius: 8rpx;
}

.add-label-btn-confirm {
  background-color: #ff0043;
  width: calc(100% - 40rpx);
}

.add-label-btn-confirm:active {
  background-color: #e6003a;
}

.add-label-btn-text {
  font-size: 32rpx;
  font-weight: 500;
  color: #ffffff;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* 响应式适配 */
/* @media (max-width: 375px) {
  .add-label-container {
    width: 560rpx;
  }
  
  .add-label-header {
    padding: 24rpx 32rpx 20rpx 32rpx;
  }
  
  .add-label-content {
    padding: 32rpx;
  }
  
  .add-label-footer {
    height: 88rpx;
  }
} */
</style> 