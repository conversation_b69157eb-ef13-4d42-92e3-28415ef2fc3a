<template>
  <view class="history-page">
    <view class="status-bar"></view>
    <!-- 顶部自定义导航栏和背景 -->
    <view class="history-header">
      <view class="history-header-bar">
        <text class="history-header-title">创作历史</text>
        <!-- <text class="history-header-manage" @click="handleManage">管理</text> -->
      </view>
      <!-- <view class="history-header-tabs">
        <text :class="['history-header-tab', activeTab === 0 ? 'history-header-tab--active' : '']" @click="changeTab(0)">全部</text>
        <text :class="['history-header-tab', activeTab === 1 ? 'history-header-tab--active' : '']" @click="changeTab(1)">视频</text>
        <text :class="['history-header-tab', activeTab === 2 ? 'history-header-tab--active' : '']" @click="changeTab(2)">穿版</text>
      </view> -->
      <HistoryFilterBar
        @filter="handleFilterChange"
        ref="filterBar"
      />
    </view>

    <!-- 可滚动内容区域 -->
    <scroll-view 
      :key="scrollViewKey"
      class="history-scroll" 
      scroll-y="true" 
      refresher-enabled="true" 
      :refresher-triggered="refreshing" 
      @refresherrefresh="onRefresh"
      show-scrollbar="false"
      enhanced="true"
      bounces="true"
      @scrolltolower="onScrollToLower"
      :scroll-into-view="scrollIntoViewId"
    >
      <view class="history-content">
        <view id="top-anchor" class="top-anchor"></view>
        <!-- 历史分组列表 -->
        <view class="history-list">
          <!-- 历史卡片组件（包含完整的历史记录项） -->
          <HistoryCard
            v-for="(group, gIdx) in historyList"
            :key="group.id"
            :item="group"
            @action="handleCardAction"
          />
        </view>

        <view class="history-nomore" v-if="!pagination.hasNext || historyList.length === 0">
          <text>暂时没有更多了</text>
        </view>
      </view>
    </scroll-view>

    <!-- 浮动创作按钮 -->
    <view class="history-create-btn-fixed" @click="goToCreate">
      <image class="history-create-btn-fixed__img" src="@/asset/img/history/btn_create.png" />
      <text class="history-create-btn-fixed__text">创作</text>
    </view>

    <!-- 分享弹窗 -->
    <view v-if="showDialog" class="history-publish-modal__mask" @click="closePublishModal" @touchmove.stop.prevent></view>
    <view v-if="showDialog" class="history-publish-modal__container" @touchmove.stop.prevent>
      <view class="history-publish-modal__title">分享至：</view>
      <view class="history-publish-modal__grid">
        <view class="history-publish-modal__item" v-for="(item, idx) in publishChannelsFull.slice(0,4)" @click="handleModalItemClick(item)" :key="item.key">
          <image class="history-publish-modal__icon" :src="$tools.processVodImageUrl(item.icon)" mode="aspectFit" />
          <text class="history-publish-modal__text">{{ item.text }}</text>
        </view>
      </view>
      <view class="history-publish-modal__grid">
        <view class="history-publish-modal__item" v-for="(item, idx) in publishChannelsFull.slice(4)" @click="handleModalItemClick(item)" :key="item.key">
          <image class="history-publish-modal__icon" :src="$tools.processVodImageUrl(item.icon)" mode="aspectFit" />
          <text class="history-publish-modal__text">{{ item.text }}</text>
        </view>
      </view>
      <image class="history-publish-modal__divider" src="@/asset/img/history/dialog-divider.png" />
      <view class="history-publish-modal__cancel" @click="closePublishModal">取消</view>
    </view>

                <!-- VideoPlayer 组件 -->
            <VideoPlayer
              v-if="showVideoPlayer"
              :video-src="videoUrl"
              :poster="videoPoster"
              :auto-play="true"
              :loop="false"
              :muted="false"
              :has-modal="showDownloadProgress"
              @close="onVideoPlayerClose"
              @play="onVideoPlay"
              @pause="onVideoPause"
              @ended="onVideoEnded"
              @timeupdate="onVideoTimeUpdate"
              @fullscreen="onVideoFullscreen"
              @export="onVideoExport"
              @error="onVideoError"
            />

            <!-- 下载进度组件 -->
            <DownloadProgress
              :visible="showDownloadProgress"
              :progress="downloadProgress"
              :file-name="downloadFileName"
              :status="downloadStatus"
              @cancel="handleCancelDownload"
            />
  </view>
  
  <!-- VideoPreviewExport 组件 -->
  <!-- <VideoPreviewExport
    v-if="showVideoPreview"
    :videoUrl="videoUrl"
    :videoTitle="videoTitle"
    :videoId="videoId"
    @close="onVideoPreviewClose"
  /> -->
</template>

<script setup>
import { ref, onUnmounted, computed, nextTick } from 'vue'
import { onShow, onHide } from '@dcloudio/uni-app'
import historyService from '../../service/history';
import douyin from '@/asset/img/history/dialog-douyin.png';
import wechat from '@/asset/img/history/dialog-wechat.png';
import xiaohongshu from '@/asset/img/history/dialog-xiaohongshu.png';
import other from '@/asset/img/history/dialog-other.png';
import download from '@/asset/img/history/dialog-download.png';
import preview from '@/asset/img/history/dialog-preview.png';
import copy from '@/asset/img/history/dialog-copy.png';
import edit from '@/asset/img/history/dialog-edit.png';
import defaultPoster from '@/asset/img/history/default_poster.png';
import VideoPreviewExport from '@/components/VideoPreviewExport.vue'
import VideoPlayer from '@/components/business/VideoPlayer/index.vue'
import DownloadProgress from '@/components/business/DownloadProgress/index.vue'
import HistoryFilterBar from './HistoryFilterBar.vue'
import HistoryCard from './HistoryCard.vue'

const videoUrl = ref('')
const videoPoster = ref('')
const videoId = ref(null)
const showVideoPlayer = ref(false)

// 下载进度相关变量
const showDownloadProgress = ref(false)
const downloadProgress = ref(0)
const downloadFileName = ref('')
const downloadStatus = ref('downloading')
const currentDownloadTask = ref(null)
// 使用scroll-into-view替代scrollTop
const scrollIntoViewId = ref('')
const activeTab = ref(0)
const refreshing = ref(false)
const scrollViewKey = ref(0) // 用于强制重新渲染 scroll-view
const loading = ref(false)
const showDialog = ref(false)
// 当前操作视频
const currentOperateVideo = ref(null)
// 创作历史列表
const historyList = ref([])
const pagination = ref({
  page: 1,
  size: 10,
  hasNext: false
})
// 轮询相关变量
const pollingTimer = ref(null)
const isPolling = ref(false)

// 处理筛选条件变更
function handleFilterChange(filters) {
  // 更新筛选条件
  dateFilter.value = filters.dateRange
  statusFilter.value = filters.status
  
  // 重新加载数据
  scrollToTop()
  stopPolling()
  uni.showLoading({ title: '加载中...', mask: true })
  fetchCreateHistroyData().finally(() => {
    uni.hideLoading()
  })
}

// 处理卡片操作
function handleCardAction({ type, item }) {
  switch (type) {
    case 'play':
      handlePlayVideo(item)
      break
    case 'delete':
      deleteHistory(item.id)
      break
    case 'download':
      handleDownload(item)
      break
    case 'publish':
      handleMaincardAction('publish', item)
      break
    case 'retry':
      handleMaincardAction('retry', item)
      break
  }
}

// 检查是否需要继续轮询
const needContinuePolling = () => {
  return historyList.value.some(item => {
    // 如果状态不是完成(4)和失败(5)，则需要继续轮询
    return item.status !== 4 && item.status !== 5
  })
}

// 开始轮询
const startPolling = () => {
  if (isPolling.value) return

  isPolling.value = true
  const poll = async () => {
    if (!isPolling.value || !needContinuePolling()) {
      stopPolling()
      return
    }

    try {
      await fetchCreateHistroyDataForPolling()
    } catch (error) {
      console.error('轮询更新失败:', error)
    }

    // 如果还在轮询状态，继续下一次轮询
    if (isPolling.value && needContinuePolling()) {
      pollingTimer.value = setTimeout(poll, 8000)
    } else {
      stopPolling()
    }
  }

  // 启动第一次轮询
  pollingTimer.value = setTimeout(poll, 8000)
}

// 停止轮询
const stopPolling = () => {
  if (pollingTimer.value) {
    clearTimeout(pollingTimer.value)
    pollingTimer.value = null
  }
  isPolling.value = false
}

const publishChannelsFull = [
  { icon: douyin, text: '抖音', key: 'douyin' },
  { icon: wechat, text: '微信', key: 'wechat' },
  { icon: xiaohongshu, text: '小红书', key: 'xiaohongshu' },
  { icon: other, text: '其他', key: 'other' },
  { icon: download, text: '下载本地', key: 'download' },
  { icon: preview, text: '预览视频', key: 'preview' },
  { icon: copy, text: '复制视频', key: 'copy' },
  { icon: edit, text: '编辑视频', key: 'edit' }
]

function handleDownload(group, definition = 'HD') {
  if (!group || !group.vod_video_id) {
    uni.showToast({ title: '无可下载文件', icon: 'none' });
    return;
  }
  
  // 先检查相册权限
  checkPhotoAlbumPermission().then(hasPermission => {
    if (hasPermission) {
      // 有权限，直接开始下载
      startDownload(group, definition);
    } else {
      // 没有权限，弹窗索要权限
      requestPhotoAlbumPermission().then(granted => {
        if (granted) {
          // 获取权限成功，开始下载
          startDownload(group, definition);
        } else {
          // 用户拒绝权限
          uni.showToast({ title: '需要相册权限才能保存视频', icon: 'none' });
        }
      });
    }
  });
}

// 检查相册权限
function checkPhotoAlbumPermission() {
  return new Promise((resolve) => {
    // #ifdef APP-PLUS
    // APP端检查存储权限
    try {
      if (typeof plus !== 'undefined' && plus.android) {
        const main = plus.android.runtimeMainActivity();
        const PackageManager = plus.android.importClass('android.content.pm.PackageManager');
        const permission = 'android.permission.WRITE_EXTERNAL_STORAGE';
        
        const granted = main.checkSelfPermission(permission) === PackageManager.PERMISSION_GRANTED;
        resolve(granted);
      } else {
        resolve(false);
      }
    } catch (error) {
      console.error('权限检查失败:', error);
      resolve(false);
    }
    // #endif
    
    // #ifdef H5
    // H5环境下默认有权限
    resolve(true);
    // #endif
    
    // #ifdef MP-WEIXIN
    // 微信小程序检查相册权限
    uni.getSetting({
      success: (res) => {
        if (res.authSetting['scope.writePhotosAlbum'] === false) {
          resolve(false);
        } else if (res.authSetting['scope.writePhotosAlbum'] === true) {
          resolve(true);
        } else {
          uni.authorize({
            scope: 'scope.writePhotosAlbum',
            success: () => resolve(true),
            fail: () => resolve(false)
          });
        }
      },
      fail: () => resolve(false)
    });
    // #endif
    
    // 其他平台默认有权限
    // #ifndef APP-PLUS
    // #ifndef H5
    // #ifndef MP-WEIXIN
    resolve(true);
    // #endif
    // #endif
    // #endif
  });
}

// 请求相册权限
function requestPhotoAlbumPermission() {
  return new Promise((resolve) => {
    uni.showModal({
      title: '需要相册权限',
      content: '为了保存视频到相册，需要您授权相册访问权限。请在设置中开启"保存到相册"权限。',
      confirmText: '去设置',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // #ifdef APP-PLUS
          // APP端直接请求权限
          if (typeof plus !== 'undefined' && plus.android) {
            const permission = 'android.permission.WRITE_EXTERNAL_STORAGE';
            plus.android.requestPermissions([permission], (resultObj) => {
              if (resultObj.granted.length > 0) {
                uni.showToast({ title: '权限获取成功', icon: 'success' });
                resolve(true);
              } else {
                uni.showToast({ title: '权限获取失败', icon: 'none' });
                resolve(false);
              }
            }, (error) => {
              uni.showToast({ title: '权限获取失败', icon: 'none' });
              resolve(false);
            });
          } else {
            uni.showToast({ title: '当前环境不支持权限请求', icon: 'none' });
            resolve(false);
          }
          // #endif
          
          // #ifdef H5
          resolve(true);
          // #endif
          
          // #ifdef MP-WEIXIN
          // 微信小程序打开设置页面
          uni.openSetting({
            success: (settingRes) => {
              if (settingRes.authSetting['scope.writePhotosAlbum'] === true) {
                uni.showToast({ title: '权限获取成功', icon: 'success' });
                resolve(true);
              } else {
                uni.showToast({ title: '请在设置中开启相册权限', icon: 'none' });
                resolve(false);
              }
            },
            fail: () => {
              uni.showToast({ title: '打开设置失败', icon: 'none' });
              resolve(false);
            }
          });
          // #endif
          
          // 其他平台默认有权限
          // #ifndef APP-PLUS
          // #ifndef H5
          // #ifndef MP-WEIXIN
          resolve(true);
          // #endif
          // #endif
          // #endif
        } else {
          resolve(false);
        }
      }
    });
  });
}

// 开始下载流程
function startDownload(group, definition = 'HD') {
  // 设置下载信息
  downloadFileName.value = group.title || '视频文件'
  downloadProgress.value = 0
  downloadStatus.value = 'downloading'
  showDownloadProgress.value = true
  
  // 获取下载链接
  historyService.getOriginalVodDownloadUrl(group.vod_video_id)
    .then(res => {
      if (res.status_code === 1 && res.data) {
        console.log('API返回数据:', res.data);
        
        let downloadUrl = res.data.download_url;
        
        // 如果有other_rates数组，根据definition查找对应的play_url
        if (res.data.other_rates && Array.isArray(res.data.other_rates)) {
          const targetRate = res.data.other_rates.find(rate => 
            rate.definition && rate.definition.toLowerCase() === definition.toLowerCase()
          );
          
          if (targetRate && targetRate.play_url) {
            downloadUrl = targetRate.play_url;
            console.log(`找到${definition}分辨率的下载链接:`, downloadUrl);
          } else {
            console.log(`未找到${definition}分辨率的下载链接，使用默认链接`);
          }
        }
        if (!downloadUrl) {
          showDownloadProgress.value = false;
          uni.showToast({ title: '获取下载链接失败', icon: 'none' });
          return;
        }
        
        // 开始下载
        const downloadTask = uni.downloadFile({
          url: downloadUrl,
          success: (res) => {
            if (res.statusCode === 200) {
              downloadStatus.value = 'completed'
              downloadProgress.value = 100
              
              // 直接保存到相册
              uni.saveVideoToPhotosAlbum({
                filePath: res.tempFilePath,
                success: () => {
                  setTimeout(() => {
                    showDownloadProgress.value = false
                    uni.showToast({ title: '已保存到相册', icon: 'success' });
                  }, 1000)
                },
                fail: (err) => {
                  downloadStatus.value = 'failed'
                  setTimeout(() => {
                    showDownloadProgress.value = false
                    if (err.errMsg && err.errMsg.includes('auth deny')) {
                      uni.showModal({
                        title: '提示',
                        content: '需要您授权保存到相册',
                        success: (modalRes) => {
                          if (modalRes.confirm) {
                            uni.openSetting()
                          }
                        }
                      })
                    } else {
                      uni.showToast({ title: '保存到相册失败', icon: 'none' });
                    }
                  }, 1000)
                }
              });
            } else {
              downloadStatus.value = 'failed'
              setTimeout(() => {
                showDownloadProgress.value = false
                uni.showToast({ title: '下载失败', icon: 'none' });
              }, 2000)
            }
          },
          fail: (err) => {
            console.error('下载失败:', err)
            downloadStatus.value = 'failed'
            setTimeout(() => {
              showDownloadProgress.value = false
              uni.showToast({ title: '下载失败', icon: 'none' });
            }, 2000)
          }
        })
        
        // 监听下载进度
        downloadTask.onProgressUpdate((res) => {
          downloadProgress.value = res.progress
        })
        
        // 保存下载任务引用，用于取消
        currentDownloadTask.value = downloadTask
        
      } else {
        showDownloadProgress.value = false
        uni.showToast({ title: res.message || '获取下载链接失败', icon: 'none' });
      }
    })
    .catch((err) => {
      console.error('获取下载链接失败:', err)
      showDownloadProgress.value = false
      uni.showToast({ title: '网络错误', icon: 'none' });
    });
}



function handlePlayVideo(group) {
  if (!group.output_path_auth) {
    uni.showToast({ title: '暂无视频', icon: 'none' });
    return;
  }
  // 隐藏tabbar
  uni.hideTabBar()
  videoUrl.value = group.output_path_auth
  videoPoster.value = group.cover_image_path_auth || defaultPoster
  videoId.value = group.vod_video_id
  showVideoPlayer.value = true
}

function onVideoPlayerClose() {
  console.log('History: VideoPlayer关闭事件触发')
  showVideoPlayer.value = false
  videoUrl.value = ''
  videoPoster.value = ''
  // 确保下载进度弹窗也关闭
  showDownloadProgress.value = false
}

// VideoPlayer 事件处理函数
function onVideoPlay() {
  console.log('视频开始播放')
}

function onVideoPause() {
  console.log('视频暂停播放')
}

function onVideoEnded() {
  console.log('视频播放结束')
}

function onVideoTimeUpdate({ current, total }) {
  console.log(`播放进度: ${current}/${total}`)
}

function onVideoFullscreen(isFullscreen) {
  console.log(`全屏状态: ${isFullscreen}`)
}

function onVideoExport(exportData) {
  // 使用当前视频ID进行导出操作
  if (videoId.value) {
    const definition = exportData?.definition || 'HD'
    // 从当前播放的视频数据中获取完整信息
    const currentVideo = historyList.value.find(item => item.vod_video_id === videoId.value)
    if (currentVideo) {
      handleDownload(currentVideo, definition)
    } else {
      // 如果找不到完整信息，创建一个最小化的对象
      handleDownload({ vod_video_id: videoId.value, title: '视频文件' }, definition)
    }
  }
}

function onVideoError(error) {
  console.error('视频播放错误:', error)
  uni.showToast({ title: '视频播放失败', icon: 'none' })
}

// 取消下载处理
function handleCancelDownload() {
  if (currentDownloadTask.value) {
    currentDownloadTask.value.abort()
    currentDownloadTask.value = null
  }
  
  downloadStatus.value = 'cancelled'
  setTimeout(() => {
    showDownloadProgress.value = false
    uni.showToast({ title: '下载已取消', icon: 'none' })
  }, 1000)
}


function handlePreview(group) {
  showDialog.value = false;
  handlePlayVideo(group);
}

const modalItemHandlerMap = {
  download: (group) => handleDownload(group, 'HD'),
  preview: handlePreview
}

// 分享弹窗点击事件
function handleModalItemClick(item) {
  if (modalItemHandlerMap[item.key]) {
    modalItemHandlerMap[item.key](currentOperateVideo.value)
  } else {
    uni.showToast({
      title: '正在开发中',
      icon: 'none'
    })
  }
}

// 开始创作
function goToCreate() {
  uni.navigateTo({ url: '/pages/create/index' })
}

const closePublishModal = () => { 
  showDialog.value = false;
  uni.showTabBar()
}

const handleManage = () => {}

const changeTab = async (index) =>{
  activeTab.value = index;
  scrollToTop();
  // 停止当前轮询
  stopPolling();
  uni.showLoading({ title: '加载中...' , mask: true});
  await fetchCreateHistroyData();
  uni.hideLoading();
}

const handleMaincardAction = (action, group) => {
  currentOperateVideo.value = group;
  if (action === 'publish') {
    showDialog.value = true;
    uni.hideTabBar();
  } else if (action === 'retry') {
    // 重试逻辑
    uni.showLoading({ title: '重试中...', mask: true });
    historyService.retryCreateHistory(group.id)
      .then(res => {
        if (res.status_code === 1) {
          uni.showToast({ title: res.data?.message || '重试成功', icon: 'success' });
          fetchCreateHistroyData().finally(() => {
            scrollToTop();
          });
        } else {
          uni.showToast({ title: res.message || '重试失败', icon: 'none' });
        }
      })
      .catch(() => {
        uni.showToast({ title: '网络错误', icon: 'none' });
      })
      .finally(() => {
        uni.hideLoading();
      });
  }
}

const showStatusDropdown = ref(false)
const statusFilter = ref(null)
const statusFilterOptions = [
  { label: '全部', value: null },
  { label: '待处理', value: 0 },
  { label: '剪辑中', value: 3 },
  { label: '生成失败', value: 5 },
  { label: '已完成', value: 4 },
]
const statusFilterLabel = computed(() => {
  const found = statusFilterOptions.find(i => i.value === statusFilter.value)
  return found ? found.label : '全部';
})

function selectStatusFilter(val) {
  if (loading.value) return; // 加载中禁止切换
  if (statusFilter.value === val) {
    showStatusDropdown.value = false
    return
  }
  statusFilter.value = val
  showStatusDropdown.value = false
  // 停止当前轮询
  stopPolling();
  uni.showLoading({ title: '加载中...', mask: true })
  fetchCreateHistroyData().finally(() => {
    scrollToTop();
    uni.hideLoading()
  })
}

// 获取今天和7天前的日期字符串
function getDateStr(date) {
  return date.toISOString().slice(0, 10)
}
const today = new Date()
const sevenDaysAgo = new Date()
sevenDaysAgo.setDate(today.getDate() - 6) // 包含今天共7天

const dateFilter = ref([getDateStr(sevenDaysAgo), getDateStr(today)])
const datePickerStart = '2020-01-01'
const datePickerEnd = new Date().toISOString().slice(0, 10)

function openDatePicker() {
  uni.hideTabBar()
}
function closeDatePicker() {
  uni.showTabBar()
}
function onDatePickerConfirm(val) {
  closeDatePicker()
  if (val && val.length === 2) {
    dateFilter.value = val
    stopPolling()
    uni.showLoading({ title: '加载中...', mask: true })
    fetchCreateHistroyData().finally(() => {
      scrollToTop()
      uni.hideLoading()
    })
  }
}
function onDatePickerClose() {
  closeDatePicker()
}

// 轮询专用的数据获取函数
const fetchCreateHistroyDataForPolling = async () => {
  // 计算当前已加载的数据总量
  const currentTotalItems = historyList.value.length
  const maxSizePerRequest = 100 // 接口限制的最大size

  // 如果当前数据量超过100，需要分批获取
  if (currentTotalItems > maxSizePerRequest) {
    await fetchDataInBatches(currentTotalItems)
  } else {
    // 直接获取当前数量的数据
    await fetchDataWithSize(Math.max(currentTotalItems, pagination.value.size))
  }
}

// 分批获取数据（当数据量超过100时）
const fetchDataInBatches = async (totalNeeded) => {
  const maxSizePerRequest = 100
  const totalPages = Math.ceil(totalNeeded / maxSizePerRequest)
  const concurrentLimit = 3 // 每组并发请求数量

  let allItems = []

  // 分组处理，每组最多3个并发请求
  for (let groupStart = 0; groupStart < totalPages; groupStart += concurrentLimit) {
    const groupEnd = Math.min(groupStart + concurrentLimit, totalPages)
    const groupPromises = []

    // 创建当前组的请求
    for (let i = groupStart; i < groupEnd; i++) {
      const currentPage = i + 1
      const currentSize = Math.min(maxSizePerRequest, totalNeeded - i * maxSizePerRequest)

      const params = {
        page: currentPage,
        size: currentSize
      }

      if (statusFilter.value !== null) {
        params.status = statusFilter.value
      }
      if (dateFilter.value && dateFilter.value[0] && dateFilter.value[1]) {
        params.start_date = dateFilter.value[0]
        params.end_date = dateFilter.value[1]
      }

      groupPromises.push(historyService.getCreateHistoryList(params))
    }

    try {
      // 并行获取当前组的数据
      const groupResponses = await Promise.all(groupPromises)

      for (const res of groupResponses) {
        if (res.status_code === 1) {
          const list = res.data?.items || []
          allItems = allItems.concat(list)
        } else {
          console.error('批次获取失败:', res.message)
        }
      }
    } catch (error) {
      console.error('批次获取异常:', error)
    }
  }

  // 智能合并数据
  mergeHistoryData(allItems)
}

// 获取指定数量的数据
const fetchDataWithSize = async (size) => {
  const params = {
    page: 1,
    size: Math.min(size, 100) // 确保不超过接口限制
  }

  if (statusFilter.value !== null) {
    params.status = statusFilter.value
  }
  if (dateFilter.value && dateFilter.value[0] && dateFilter.value[1]) {
    params.start_date = dateFilter.value[0]
    params.end_date = dateFilter.value[1]
  }

  try {
    const res = await historyService.getCreateHistoryList(params)
    if (res.status_code === 1) {
      const list = res.data?.items || []
      // 智能合并数据
      mergeHistoryData(list)
    }
  } catch (error) {
    console.error('轮询获取数据失败:', error)
  }
}

// 智能数据合并函数
const mergeHistoryData = (newData) => {
  if (!newData || newData.length === 0) return

  // 创建新数据的映射，用于快速查找
  const newDataMap = new Map()
  newData.forEach(item => {
    newDataMap.set(item.id, item)
  })

  // 创建当前数据的映射
  const currentDataMap = new Map()
  historyList.value.forEach((item, index) => {
    currentDataMap.set(item.id, { item, index })
  })

  // 更新现有数据并收集新数据
  const updatedList = [...historyList.value]
  const newItems = []

  // 检查现有数据并根据状态判断是否覆盖，同时收集新数据
  for (const [id, newItem] of newDataMap) {
    if (currentDataMap.has(id)) {
      const { item: currentItem, index } = currentDataMap.get(id)
      
      // 只有当旧数据不是已完成(4)或失败(5)状态时才更新
      if (currentItem.status !== 4 && currentItem.status !== 5) {
        updatedList[index] = newItem
      }
    } else {
      // 收集新数据项
      newItems.push(newItem)
    }
  }

  // 将新数据项添加到列表开头
  if (newItems.length > 0) {
    updatedList.unshift(...newItems)
  }

  // 更新列表
  historyList.value = updatedList
}

// 获取创作历史列表
const fetchCreateHistroyData = async (isLoadMore = false) => {
  if (loading.value) return;
  loading.value = true;

  // 如果是加载更多，页码+1，否则重置为1
  if (isLoadMore) {
    pagination.value.page += 1;
  } else {
    pagination.value.page = 1;
  }

  const params = {
    page: pagination.value.page,
    size: pagination.value.size
  }
  if (statusFilter.value !== null) {
    params.status = statusFilter.value
  }
  if (dateFilter.value && dateFilter.value[0] && dateFilter.value[1]) {
    params.start_date = dateFilter.value[0]
    params.end_date = dateFilter.value[1]
  }

  try {
    const res = await historyService.getCreateHistoryList(params)
    if (res.status_code === 1) {
      const list = res.data?.items || []
      if (isLoadMore) {
        historyList.value = historyList.value.concat(list)
      } else {
        historyList.value = list
      }
      // 更新分页信息
      if (res.pagination) {
        pagination.value.hasNext = res.pagination.has_next
      }

      if (needContinuePolling()) {
        startPolling()
      } else {
        stopPolling()
      }
    } else {
      // 获取失败重置分页
      if (isLoadMore) {
        pagination.value.page -= 1;
      }
      uni.showToast({ title: res.message || '获取失败', icon: 'none' })
    }
  } catch (e) {
    console.log(e, 'fetchError');
    if (isLoadMore) {
      pagination.value.page -= 1;
    }
    uni.showToast({ title: '网络错误', icon: 'none' })
  } finally {
    loading.value = false
  }
}

// 下拉刷新处理
const onRefresh = async () => {
  refreshing.value = true
  // 下拉刷新时停止轮询
  stopPolling()
  await fetchCreateHistroyData()
  refreshing.value = false
}

// 删除创作历史
const deleteHistory = async (taskId) => {
  uni.showModal({
    title: '提示',
    content: '确定要删除该创作历史吗？',
    success: async (res) => {
      if (res.confirm) {
        // 删除前停止轮询
        stopPolling()
        const res = await historyService.deleteCreateHistory(taskId)
        if (res.status_code === 1) {
          uni.showToast({ title: '删除成功', icon: 'success' })
          await fetchCreateHistroyData()
          scrollToTop();
        } else {
          uni.showToast({ title: res.message || '删除失败', icon: 'none' })
        }
      }
    }
  })
}

// 触底加载
const onScrollToLower = () => {
  if (pagination.value.hasNext && !loading.value) {
    fetchCreateHistroyData(true)
  }
}


onUnmounted(() => {
  stopPolling()
  // 清理下载任务
  if (currentDownloadTask.value) {
    currentDownloadTask.value.abort()
    currentDownloadTask.value = null
  }
})

// 滚动到顶部的函数
const scrollToTop = () => {
  // 清空后再设置ID，确保每次都能触发滚动
  scrollIntoViewId.value = ''
  
  // 使用nextTick和小延时确保触发滚动
  nextTick(() => {
    setTimeout(() => {
      scrollIntoViewId.value = 'top-anchor'
    }, 10)
  })
}

// 移除原来的生命周期函数，使用 scroll-view 的内置功能

onShow(() => {
  uni.setTabBarStyle({
    color: 'rgba(140, 140, 140, 1)',
    selectedColor: '#FF0043',
    backgroundColor: '#fff',
    borderStyle: 'white'
  })
  
  scrollToTop();
  // 页面展示重新请求数据，并检查是否轮询
  fetchCreateHistroyData();
})

onHide(() => {
  // 页面隐藏时停止轮询以节省资源
  stopPolling()
})
</script>

<style lang="scss" scoped>
.uni-scroll-view-refresher {
  background-color: #F9FAFB !important;
}

.status-bar {
  height: var(--status-bar-height);
  width: 100%;
}



// 顶部渐变背景
.history-header {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  z-index: 10;
  background: url('@/asset/img/history/bg_top.png') no-repeat;
  background-size: 140% 140%;
  padding-top: var(--status-bar-height);
}

.history-header-bar {
  height: 88rpx;
  background: transparent;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
  margin-top: 24rpx;
}
.history-header-title {
  font-size: 34rpx;
  flex: 1;
  // margin-left: 64rpx;
  text-align: center;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  color: #222;
  line-height: 48rpx;
}
.history-header-manage {
  font-size: 32rpx;
  color: #262626;
  font-weight: 400;
  line-height: 44rpx;
}

// Tabs
.history-header-tabs {
  background: transparent;
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  margin-top: 24rpx;
  padding: 0 32rpx;
  gap: 48rpx;
}
.history-header-tab {
  font-size: 32rpx;
  color: #8c8c8c;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  line-height: 44rpx;
  position: relative;
  &--active {
    color: #262626;
    font-weight: 500;
    &::after {
      content: '';
      display: block;
      position: absolute;
      left: 0;
      bottom: 0;
      opacity: 0.64;
      width: 100%;
      height: 10rpx;
      background: #ff0043;
      border-radius: 3rpx;
    }
  }
}

// 筛选区
.history-header-filters {
  background: transparent;
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: center;
  margin-top: 24rpx;
  margin-bottom: 32rpx;
  padding-left: 32rpx;
  gap: 24rpx;
}
.history-header-filter {
  background: #fff;
  border-radius: 8rpx;
  padding: 6rpx 0rpx 6rpx 20rpx;
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color:#595959;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  &--active {
    color: #ff0043;
  }
}

.history-header-filter-wrapper {
  position: relative;
  display: inline-block;
}

// 滚动区域样式
.history-scroll {
  position: absolute;
  // top: calc(280rpx + var(--status-bar-height)); /* 头部实际高度 */
  top: calc(212rpx + var(--status-bar-height));
  left: 0;
  right: 0;
  bottom: 0;
  overflow-y: auto;
  background: #f9f9f9;
  min-height: 200rpx; /* 确保有最小高度 */
  -webkit-overflow-scrolling: touch; /* iOS 滚动优化 */
}

.history-content {
  padding: 24rpx;
  padding-bottom: 48rpx;
  min-height: 120vh; /* 确保内容高度足够触发滚动 */
  padding-top: 0;
}

.top-anchor {
  height: 1px;
  width: 100%;
  opacity: 0;
  position: relative;
  top: 0;
}

// 历史分组
.history-list {
  padding: 0;
}
// .history-group 样式已移至 HistoryCard.vue 组件
// 卡片区样式已移至 HistoryCard.vue 组件

.history-nomore {
  font-weight: 400;
  font-size: 28rpx;
  color: #BFBFBF;
  line-height: 40rpx;
  text-align: center;
  margin-top: 40rpx;
}

// 浮动创作按钮
.history-create-btn-fixed {
  position: fixed;
  right: 48rpx;
  bottom: 120rpx;
  z-index: 9;
  background: linear-gradient(90deg, #ff2d55 0%, #ff6b6b 100%);
  border-radius: 44rpx;
  width: 180rpx;
  height: 80rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  box-shadow: 0px 2rpx 7rpx 0px rgba(0, 0, 0, 0.11);
  .history-create-btn-fixed__img {
    width: 32rpx;
    height: 32rpx;
    margin-right: 12rpx;
  }
  .history-create-btn-fixed__text {
    color: #fff;
    font-size: 32rpx;
    font-family: PingFangSC-Medium;
    font-weight: 500;
    line-height: 44rpx;
  }
}
.history-publish-modal__mask {
  position: fixed;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0,0,0,0.5);
  z-index: 101;
}
.history-publish-modal__container {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100vw;
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  padding: 38rpx 40rpx 22rpx 30rpx;
  z-index: 110;
  box-sizing: border-box;
  animation: modalUp 0.2s;
}
@keyframes modalUp {
  from { transform: translateY(100%); }
  to { transform: translateY(0); }
}
.history-publish-modal__title {
  color: #262626;
  font-size: 24rpx;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 34rpx;
  margin-bottom: 40rpx;
}
.history-publish-modal__grid {
  width: 552rpx;
  display: flex;
  flex-wrap: wrap;
  margin: 0 40rpx 40rpx 0;
}
.history-publish-modal__item {
  width: 108rpx;
  height: 148rpx;
  margin: 0 40rpx 40rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
}
.history-publish-modal__item:nth-child(4n) {
  margin-right: 0;
}
.history-publish-modal__item:nth-last-child(-n + 4) {
  margin-bottom: 0;
}
.history-publish-modal__icon {
  width: 108rpx;
  height: 108rpx;
}
.history-publish-modal__text {
  color: #595959;
  font-size: 20rpx;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
  margin-top: 12rpx;
}
.history-publish-modal__divider {
  width: 670rpx;
  height: 2rpx;
  background: #f2f2f2;
}
.history-publish-modal__cancel {
  color: #262626;
  font-size: 32rpx;
  text-align: center;
  line-height: 44rpx;
  padding: 20rpx 302rpx 0 314rpx;
  font-weight: normal;
  cursor: pointer;
}



</style>
