import materialService from './material'

// 本地存储键名
const STORAGE_KEY = 'preGeneratedDocs'
const GENERATING_FLAG_KEY = 'isGeneratingDocs'

class DocGeneratorService {
  constructor() {
    this.generatingPromise = null
  }

  /**
   * 生成默认文案（无loading弹窗）
   * @param {Object} options 生成选项
   * @returns {Promise<Array>} 返回生成的文案数组
   */
  async generateDefaultDocs(options = {}) {
    const {
      function_type = 1,
      prompt_type = 'default',
      custom_requirements = '请生成一条适合短视频的吸引人的文案',
      compute_account_type = 1
    } = options

    try {
      // 设置生成中标志
      uni.setStorageSync(GENERATING_FLAG_KEY, true)
      
      const res = await materialService.generateDoc({
        function_type,
        prompt_type,
        custom_requirements,
        origin_text: '',
        compute_account_type
      })

      if (res && res.status_code === 1 && res.data && res.data.result_text) {
        const docs = [{ content: res.data.result_text }]
        // 保存到本地存储
        uni.setStorageSync(STORAGE_KEY, docs)
        return docs
      } else {
        console.error('生成默认文案失败:', res?.message)
        return []
      }
    } catch (error) {
      console.error('生成默认文案异常:', error)
      return []
    } finally {
      // 清除生成中标志
      uni.removeStorageSync(GENERATING_FLAG_KEY)
    }
  }

  /**
   * 生成文案（带loading弹窗）
   * @param {Object} options 生成选项
   * @returns {Promise<Array>} 返回生成的文案数组
   */
  async generateDocsWithLoading(options = {}) {
    const {
      function_type = 1,
      origin_text = '',
      prompt_type = '',
      custom_requirements = '',
      loadingTitle = '文案生成中...',
      compute_account_type = 1
    } = options

    // 检查是否正在生成中
    const isGenerating = uni.getStorageSync(GENERATING_FLAG_KEY)
    if (isGenerating) {
      // 如果正在生成中，等待生成完成
      uni.showLoading({ 
        title: '等待文案生成完成...',
        mask: true
      })
      // 等待生成完成
      while (uni.getStorageSync(GENERATING_FLAG_KEY)) {
        await new Promise(resolve => setTimeout(resolve, 500))
      }
      uni.hideLoading()
      // 返回本地存储的文案
      const storedDocs = uni.getStorageSync(STORAGE_KEY)
      return storedDocs || []
    }

    // 正常生成流程
    uni.showLoading({ 
      title: loadingTitle,
      mask: true
    })
    
    try {
      const res = await materialService.generateDoc({
        function_type,
        prompt_type,
        custom_requirements,
        origin_text,
        compute_account_type
      })

      if (res && res.status_code === 1 && res.data && res.data.result_text) {
        const docs = [{ content: res.data.result_text }]
        // 更新本地存储
        uni.setStorageSync(STORAGE_KEY, docs)
        return docs
      } else {
        uni.showToast({ title: res?.message || '生成失败', icon: 'none' })
        return []
      }
    } catch (error) {
      uni.showToast({ title: error?.message || '生成失败', icon: 'none' })
      return []
    } finally {
      uni.hideLoading()
    }
  }

  /**
   * 获取本地存储的文案
   * @returns {Array} 返回本地存储的文案数组
   */
  getStoredDocs() {
    try {
      const docs = uni.getStorageSync(STORAGE_KEY)
      return Array.isArray(docs) ? docs : []
    } catch (error) {
      console.error('获取本地文案失败:', error)
      return []
    }
  }

  /**
   * 清除本地存储的文案
   */
  clearStoredDocs() {
    try {
      uni.removeStorageSync(STORAGE_KEY)
      uni.removeStorageSync(GENERATING_FLAG_KEY)
    } catch (error) {
      console.error('清除本地文案失败:', error)
    }
  }

  /**
   * 检查是否正在生成文案
   * @returns {boolean} 是否正在生成
   */
  isGenerating() {
    return !!uni.getStorageSync(GENERATING_FLAG_KEY)
  }

  /**
   * 保存文案数据到本地存储
   * @param {Array} docs 文案数组
   */
  saveStoredDocs(docs) {
    try {
      if (Array.isArray(docs)) {
        uni.setStorageSync(STORAGE_KEY, docs)
        console.log('文案数据已同步到本地存储')
      } else {
        console.error('保存文案数据失败: 数据格式不正确', docs)
      }
    } catch (error) {
      console.error('保存文案数据到本地存储失败:', error)
    }
  }
}

// 创建单例实例
const docGeneratorService = new DocGeneratorService()

export default docGeneratorService 