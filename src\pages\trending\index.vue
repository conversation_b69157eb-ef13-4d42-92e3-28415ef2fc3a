<template>
  <view class="trending-page">
    <!-- 开发中蒙层 -->
    <view class="developing-overlay">
      <!-- 左上角渐变 -->
      <view class="gradient-overlay"></view>
      <!-- 主要内容 -->
      <view class="developing-content">
        <image class="developing-image" src="@/asset/img/trending/coding.png" />
        <view class="developing-text-group">
          <text class="developing-text-main">功能正在开发中</text>
          <text class="developing-text-sub">敬请期待</text>
        </view>
      </view>
    </view>
    
    <!-- 背景图移动到trending-page -->
    <!-- 页面头部 -->
    <view class="trending-header">
      <view class="search-section">
        <view class="search-bar">
          <image class="search-icon" src="@/asset/img/trending/search.png" />
          <input 
            class="search-input" 
            placeholder="服装展示视频" 
            placeholder-class="search-placeholder"
            v-model="searchKeyword"
            @input="handleSearch"
          />
        </view>
        <view class="follow-btn" @click="handleFollow">
          <image class="follow-icon" src="@/asset/img/trending/user.png" />
          <text class="follow-text">关注</text>
        </view>
      </view>
      
      <!-- 分类导航 -->
      <view class="category-nav">
        <scroll-view class="category-scroll" scroll-x="true" show-scrollbar="false" :scroll-into-view="scrollIntoViewId">
          <view class="category-list">
            <view 
              v-for="(category, index) in categories" 
              :key="index"
              class="category-item"
              :class="{ active: activeCategory === category.id }"
              @click="selectCategory(category.id)"
              :id="'category-item-' + category.id"
            >
              <text class="category-text">{{ category.name }}</text>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>

    <scroll-view class="trending-scroll" scroll-y="true">
      <view class="trending-content">
        <!-- 空状态 -->
        <view v-if="!loading && filteredVideos.length === 0" class="empty-state">
          <!-- <image class="empty-icon" src="@/asset/img/trending/empty.png" /> -->
          <text class="empty-text">暂无相关视频</text>
          <text class="empty-desc">试试其他分类或关键词</text>
        </view>
        
        <!-- 视频网格 -->
        <view v-else class="video-grid">
          <view 
            v-for="video in filteredVideos" 
            :key="video.id"
            class="video-card"
            @click="handleVideoClick(video)"
          >
            <!-- 视频封面 -->
            <view class="video-cover">
              <image class="cover-image" :src="$tools.processVodImageUrl(video.cover)" mode="aspectFill" />
              
              <!-- 视频信息覆盖层 -->
              <view class="video-overlay">
                <!-- 点赞数 -->
                <view class="like-count">
                  <image class="like-icon" src="@/asset/img/trending/heart.png" />
                  <text class="like-text">{{ video.likes }}</text>
                </view>
                
                <!-- 拍同款按钮 -->
                <view class="action-btn" @click.stop="handleShootSame(video)">
                  <image class="action-icon" src="@/asset/img/trending/camera.png" />
                  <text class="action-text">拍同款</text>
                </view>
              </view>
              
              <!-- 标签 -->
              <view v-if="video.tag" class="video-tag">
                <text class="tag-text">{{ video.tag }}</text>
              </view>
            </view>
            
            <!-- 视频描述 -->
            <view class="video-desc">
              <text class="desc-text">{{ video.description }}</text>
            </view>
          </view>
        </view>
        
        <!-- 加载更多 -->
        <view v-if="hasMore && filteredVideos.length > 0" class="load-more" @click="loadMore">
          <text v-if="!loading" class="load-more-text">加载更多</text>
          <view v-else class="loading-indicator">
            <text class="loading-text">加载中...</text>
          </view>
        </view>
        
        <!-- 没有更多数据 -->
        <view v-else-if="filteredVideos.length > 0" class="no-more">
          <text class="no-more-text">没有更多数据了</text>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { onShow, onPullDownRefresh, onReachBottom } from '@dcloudio/uni-app'
// import { mockTrendingVideos, mockCategories } from '@/service/trending.js'

// 响应式数据
const searchKeyword = ref('')
const activeCategory = ref('all')
const scrollIntoViewId = ref('')
const indicatorLeft = ref(178)
const hasMore = ref(true)
const loading = ref(false)
const searchTimer = ref(null)

// 分类数据
const categories = ref([])

// 视频列表数据
const videoList = ref([])
const currentPage = ref(1)
const pageSize = ref(10)

// 计算属性
const filteredVideos = computed(() => {
  return videoList.value
})

// 本地模拟数据
const mockCategories = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: 200,
        data: [
          { id: 'all', name: '全部', count: 156 },
          { id: 'women', name: '女装', count: 89 },
          { id: 'children', name: '童装', count: 34 },
          { id: 'men', name: '男装', count: 23 },
          { id: 'persona', name: '人设', count: 12 },
          { id: 'store', name: '店设', count: 8 }
        ],
        message: '获取成功'
      })
    }, 300)
  })
}

// 数据获取方法
const fetchCategories = async () => {
  try {
    const response = await mockCategories()
    if (response.code === 200) {
      categories.value = response.data
      // 保证activeCategory为'all'（防止异步后被覆盖）
      if (!activeCategory.value || !categories.value.find(c => c.id === activeCategory.value)) {
        activeCategory.value = 'all'
      }
    }
  } catch (error) {
    console.error('获取分类失败:', error)
  }
}

const mockTrendingVideos = (params = {}) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const mockData = [
        {
          id: 1,
          cover: 'https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/ff2dc603c7c44b9cac5aa54cf6cb8a3e_mergeImage.png',
          likes: '9999',
          tag: '利润走量款中等毛利',
          description: '当你老了你会如何回答以下…',
          category: 'women'
        },
        {
          id: 2,
          cover: 'https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/ec62f6ebd23a46588b9f66dc7838c5bb_mergeImage.png',
          likes: '8888',
          tag: '利润走量款中等毛利',
          description: '当你老了你会如何回答以下…',
          category: 'women'
        },
        {
          id: 3,
          cover: 'https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/c753eb05babb4f958242471bcc42cadc_mergeImage.png',
          likes: '7777',
          tag: '利润走量款中等毛利',
          description: '在深圳龙华，你只要月薪超…',
          category: 'women'
        },
        {
          id: 4,
          cover: 'https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/ab620554f5ff42f3817a778370bb333e_mergeImage.png',
          likes: '6666',
          tag: '利润走量款中等毛利',
          description: '在深圳龙华，你只要月薪超…',
          category: 'women'
        }
      ]
      
      // 根据分类过滤
      let filteredData = mockData
      if (params.category && params.category !== 'all') {
        filteredData = mockData.filter(item => item.category === params.category)
      }
      
      // 根据关键词搜索
      if (params.keyword) {
        filteredData = filteredData.filter(item => 
          item.description.includes(params.keyword) || 
          item.tag.includes(params.keyword)
        )
      }
      
      resolve({
        code: 200,
        data: {
          list: filteredData,
          total: filteredData.length,
          hasMore: params.page < 3 // 模拟只有3页数据
        },
        message: '获取成功'
      })
    }, 500) // 模拟网络延迟
  })
}

const fetchVideos = async (isRefresh = false) => {
  if (loading.value) return
  
  loading.value = true
  try {
    const params = {
      category: activeCategory.value,
      keyword: searchKeyword.value,
      page: isRefresh ? 1 : currentPage.value,
      pageSize: pageSize.value
    }
    
    const response = await mockTrendingVideos(params)
    if (response.code === 200) {
      if (isRefresh) {
        videoList.value = response.data.list
        currentPage.value = 1
      } else {
        videoList.value.push(...response.data.list)
        currentPage.value++
      }
      hasMore.value = response.data.hasMore
    } else {
      throw new Error(response.message || '获取数据失败')
    }
  } catch (error) {
    console.error('获取视频列表失败:', error)
    uni.showToast({
      title: error.message || '获取数据失败',
      icon: 'none',
      duration: 2000
    })
  } finally {
    loading.value = false
  }
}

// 方法
const handleSearch = () => {
  console.log('搜索关键词:', searchKeyword.value)
  // 清除之前的定时器
  if (searchTimer.value) {
    clearTimeout(searchTimer.value)
  }
  
  // 设置防抖延迟
  searchTimer.value = setTimeout(() => {
    fetchVideos(true)
  }, 500)
}

const handleFollow = () => {
  uni.showToast({
    title: '关注功能开发中',
    icon: 'none'
  })
}

const selectCategory = (categoryId) => {
  activeCategory.value = categoryId
  scrollIntoViewId.value = 'category-item-' + categoryId
  // 计算指示器位置
  const categoryIndex = categories.value.findIndex(cat => cat.id === categoryId)
  indicatorLeft.value = 178 + categoryIndex * 136 // 根据设计稿计算位置
  // 切换分类时重新获取数据
  fetchVideos(true)
}

const handleVideoClick = (video) => {
  console.log('点击视频:', video)
  uni.navigateTo({
    url: '/pages/trending/preview'
  })
}

const handleShootSame = (video) => {
  console.log('拍同款:', video)
  // TODO: 跳转到拍摄页面
  uni.navigateTo({
    url: `/pages/create/index?template=${video.id}`
  })
}

const loadMore = async () => {
  if (hasMore.value && !loading.value) {
    await fetchVideos(false)
  }
}

// 生命周期
onMounted(async () => {
  // 初始化数据
  console.log('爆款页面初始化')
  await fetchCategories()
  await fetchVideos(true)
})

onShow(() => {
  // 设置tabbar样式
  uni.setTabBarStyle({
    color: 'rgba(140, 140, 140, 1)',
    selectedColor: '#FF0043',
    backgroundColor: '#fff',
    borderStyle: 'white'
  })
})

onPullDownRefresh(async () => {
  // 下拉刷新
  await fetchVideos(true)
  uni.stopPullDownRefresh()
  uni.showToast({
    title: '刷新成功',
    icon: 'success'
  })
})

onReachBottom(() => {
  // 触底加载更多
  if (hasMore.value && !loading.value) {
    loadMore()
  }
})
</script>

<style lang="scss" scoped>
@use '@/styles/variables.scss' as *;

.trending-page {
  position: relative;
  height: 100vh;
  background-color: #fff;
  background: url('@/asset/img/trending/bg2x.png');
  width: 100vw;
}

// 开发中蒙层样式
.developing-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #111113;
  z-index: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}

// 左上角渐变
.gradient-overlay {
  position: absolute;
  top: -42rpx;
  left: -42rpx;
  width: 378rpx;
  height: 300rpx;
  background: radial-gradient(ellipse at 30% 25%, #FF1A46 0%, rgba(255, 26, 70, 0.6) 30%, rgba(255, 26, 70, 0.3) 60%, rgba(255, 26, 70, 0) 100%);
  opacity: 0.42;
  filter: blur(100rpx);
  z-index: 1;
  pointer-events: none;
}

// 主要内容
.developing-content {
  position: absolute;
  top: 394rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 2;
}

.developing-image {
  width: 240rpx;
  height: 240rpx;
  margin-bottom: 2rpx;
}

.developing-text-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 224rpx;
}

.developing-text-main {
  color: rgba(255, 255, 255, 1);
  font-size: 32rpx;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}

.developing-text-sub {
  color: rgba(191, 191, 191, 1);
  font-size: 28rpx;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 40rpx;
  margin-top: 20rpx;
}

.trending-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 10;
  /* 移除背景图相关样式 */
  padding: 100rpx 24rpx 0;
  
  .search-section {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 38rpx;
    
    .search-bar {
      flex: 1;
      background-color: #fff;
      border-radius: 36rpx;
      padding: 16rpx 24rpx;
      display: flex;
      align-items: center;
      margin-right: 32rpx;
      
      .search-icon {
        width: 28rpx;
        height: 28rpx;
        margin-right: 12rpx;
      }
      
      .search-input {
        flex: 1;
        font-size: 28rpx;
        color: #262626;
        border: none;
        outline: none;
      }
      
      .search-placeholder {
        color: #8c8c8c;
      }
    }
    
    .follow-btn {
      display: flex;
      flex-direction: column;
      align-items: center;
      
      .follow-icon {
        width: 30rpx;
        height: 30rpx;
        margin-bottom: 8rpx;
      }
      
      .follow-text {
        font-size: 20rpx;
        color: #262626;
        line-height: 24rpx;
      }
    }
  }
  
  .category-nav {
    position: relative;
    margin-bottom: 6rpx;
    
    .category-scroll {
      white-space: nowrap;
      overflow-x: auto;
      -webkit-overflow-scrolling: touch;
      
      .category-list {
        display: flex;
        padding: 0 34rpx;
        white-space: nowrap;
        
        .category-item {
          margin-right: 68rpx;
          padding: 12rpx 0;
          position: relative;
          flex-shrink: 0;
          text-align: center;
          min-width: 60rpx;
          
          &:last-child {
            margin-right: 0;
          }
          
          .category-text {
            font-size: 30rpx;
            color: #8c8c8c;
            line-height: 42rpx;
            transition: color 0.3s ease;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            display: block;
          }
          
          &.active {
            .category-text {
              color: #262626;
              font-weight: 500;
            }
            &::after {
              content: '';
              position: absolute;
              left: 50%;
              bottom: 0;
              transform: translateX(-50%);
              width: 30rpx;
              height: 6rpx;
              background: linear-gradient(90deg, #ff2d55 0%, #ff6b6b 100%);
              border-radius: 3rpx;
              transition: all 0.3s;
            }
          }
        }
      }
    }
  }
}

.trending-scroll {
  position: absolute;
  top: 270rpx; /* 头部实际高度 */
  left: 0;
  right: 0;
  bottom: 0;
  overflow-y: auto;
  padding: 24rpx;
}

.trending-content {
  padding: 24rpx;
  
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 120rpx 0;
    
    .empty-icon {
      width: 120rpx;
      height: 120rpx;
      margin-bottom: 24rpx;
      opacity: 0.6;
    }
    
    .empty-text {
      font-size: 32rpx;
      color: #8c8c8c;
      margin-bottom: 12rpx;
    }
    
    .empty-desc {
      font-size: 24rpx;
      color: #c0c0c0;
    }
  }
  
  .video-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24rpx;
    
    .video-card {
      border-radius: 16rpx;
      overflow: hidden;
      background: #fff;
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
      
      .video-cover {
        position: relative;
        width: 100%;
        height: 452rpx;
        
        .cover-image {
          width: 340rpx;
          height: 452rpx;
          border-radius: 16rpx 16rpx 0 0;
          object-fit: cover;
        }
        
        .video-overlay {
          position: absolute;
          bottom: 20rpx;
          left: 24rpx;
          right: 20rpx;
          display: flex;
          justify-content: space-between;
          align-items: center;
          
          .like-count {
            display: flex;
            align-items: center;
            
            .like-icon {
              width: 22rpx;
              height: 20rpx;
              margin-right: 8rpx;
            }
            
            .like-text {
              font-size: 24rpx;
              color: #fff;
              line-height: 28rpx;
            }
          }
          
          .action-btn {
            background: rgba(0, 0, 0, 0.3);
            border: 0.4rpx solid rgba(255, 255, 255, 0.48);
            border-radius: 20rpx;
            padding: 8rpx 16rpx;
            display: flex;
            align-items: center;
            
            .action-icon {
              width: 20rpx;
              height: 20rpx;
              margin-right: 8rpx;
            }
            
            .action-text {
              font-size: 20rpx;
              color: #fff;
              font-weight: 500;
              line-height: 32rpx;
            }
          }
        }
        
        .video-tag {
          position: absolute;
          bottom: 80rpx;
          left: 24rpx;
          background: rgba(255, 255, 255, 0.9);
          padding: 8rpx 12rpx;
          border-radius: 8rpx;
          
          .tag-text {
            font-size: 20rpx;
            color: #ff2d55;
            font-weight: 500;
          }
        }
      }
      
      .video-desc {
        padding: 12rpx 16rpx 16rpx;
        
        .desc-text {
          font-size: 24rpx;
          color: #262626;
          line-height: 34rpx;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
      }
    }
  }
  
  .load-more {
    text-align: center;
    padding: 32rpx 0;
    
    .load-more-text {
      font-size: 28rpx;
      color: #8c8c8c;
    }
    
    .loading-indicator {
      display: flex;
      align-items: center;
      justify-content: center;
      
      .loading-text {
        font-size: 28rpx;
        color: #8c8c8c;
      }
    }
  }
  
  .no-more {
    text-align: center;
    padding: 32rpx 0;
    
    .no-more-text {
      font-size: 24rpx;
      color: #c0c0c0;
    }
  }
}

// 响应式适配
@media (max-width: 750rpx) {
  .trending-content .video-grid {
    grid-template-columns: 1fr;
  }
}
</style> 