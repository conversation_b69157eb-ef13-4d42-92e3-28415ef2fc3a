/**
 * 通用工具类
 */

// 防抖函数
export const debounce = (func, delay = 300) => {
  let timeoutId
  return function (...args) {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func.apply(this, args), delay)
  }
}

// 节流函数
export const throttle = (func, delay = 300) => {
  let lastExecTime = 0
  return function (...args) {
    const currentTime = Date.now()
    if (currentTime - lastExecTime > delay) {
      func.apply(this, args)
      lastExecTime = currentTime
    }
  }
}

// 深拷贝
export const deepClone = (obj) => {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj)
  if (obj instanceof Array) return obj.map(item => deepClone(item))
  if (typeof obj === 'object') {
    const clonedObj = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }
}

// 格式化时间
export const formatTime = (timestamp, format = 'YYYY-MM-DD HH:mm:ss') => {
  const date = new Date(timestamp)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  
  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
}

// 格式化文件大小
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 生成UUID
export const generateUUID = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0
    const v = c === 'x' ? r : (r & 0x3 | 0x8)
    return v.toString(16)
  })
}

// 验证手机号
export const validatePhone = (phone) => {
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(phone)
}

// 验证邮箱
export const validateEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

// 验证身份证号
export const validateIdCard = (idCard) => {
  const idCardRegex = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/
  return idCardRegex.test(idCard)
}

// 获取URL参数
export const getQueryParams = (url = window.location.href) => {
  const params = {}
  const queryString = url.split('?')[1]
  if (queryString) {
    queryString.split('&').forEach(param => {
      const [key, value] = param.split('=')
      params[decodeURIComponent(key)] = decodeURIComponent(value || '')
    })
  }
  return params
}

// 存储相关工具
export const storage = {
  // 设置存储
  set(key, value, expire = null) {
    const data = {
      value,
      expire: expire ? Date.now() + expire * 1000 : null
    }
    try {
      uni.setStorageSync(key, JSON.stringify(data))
    } catch (error) {
      console.error('存储设置失败:', error)
    }
  },
  
  // 获取存储
  get(key) {
    try {
      const data = uni.getStorageSync(key)
      if (!data) return null
      
      const parsedData = JSON.parse(data)
      if (parsedData.expire && Date.now() > parsedData.expire) {
        this.remove(key)
        return null
      }
      
      return parsedData.value
    } catch (error) {
      console.error('存储获取失败:', error)
      return null
    }
  },
  
  // 删除存储
  remove(key) {
    try {
      uni.removeStorageSync(key)
    } catch (error) {
      console.error('存储删除失败:', error)
    }
  },
  
  // 清空存储
  clear() {
    try {
      uni.clearStorageSync()
    } catch (error) {
      console.error('存储清空失败:', error)
    }
  }
}

// 设备信息获取
export const getDeviceInfo = () => {
  try {
    const systemInfo = uni.getSystemInfoSync()
    return {
      platform: systemInfo.platform,
      system: systemInfo.system,
      version: systemInfo.version,
      model: systemInfo.model,
      brand: systemInfo.brand,
      pixelRatio: systemInfo.pixelRatio,
      screenWidth: systemInfo.screenWidth,
      screenHeight: systemInfo.screenHeight,
      windowWidth: systemInfo.windowWidth,
      windowHeight: systemInfo.windowHeight,
      statusBarHeight: systemInfo.statusBarHeight,
      safeArea: systemInfo.safeArea,
      safeAreaInsets: systemInfo.safeAreaInsets
    }
  } catch (error) {
    console.error('获取设备信息失败:', error)
    return {}
  }
}

// 权限检查
export const checkPermission = (permission) => {
  return new Promise((resolve) => {
    uni.getSetting({
      success: (res) => {
        resolve(res.authSetting[permission] === true)
      },
      fail: () => {
        resolve(false)
      }
    })
  })
}

// 请求权限
export const requestPermission = (permission) => {
  return new Promise((resolve) => {
    uni.authorize({
      scope: permission,
      success: () => {
        resolve(true)
      },
      fail: () => {
        resolve(false)
      }
    })
  })
}

// 图片压缩
export const compressImage = (src, quality = 0.8) => {
  return new Promise((resolve, reject) => {
    uni.compressImage({
      src,
      quality,
      success: (res) => {
        resolve(res.tempFilePath)
      },
      fail: (error) => {
        reject(error)
      }
    })
  })
}

// 选择图片
export const chooseImage = (options = {}) => {
  const defaultOptions = {
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera']
  }
  
  return new Promise((resolve, reject) => {
    uni.chooseImage({
      ...defaultOptions,
      ...options,
      success: (res) => {
        resolve(res.tempFilePaths)
      },
      fail: (error) => {
        reject(error)
      }
    })
  })
}

// 预览图片
export const previewImage = (urls, current = 0) => {
  uni.previewImage({
    urls,
    current: typeof current === 'number' ? urls[current] : current
  })
}

// 显示加载提示
export const showLoading = (title = '加载中...', mask = true) => {
  uni.showLoading({
    title,
    mask
  })
}

// 隐藏加载提示
export const hideLoading = () => {
  uni.hideLoading()
}

// 显示消息提示
export const showToast = (title, icon = 'none', duration = 2000) => {
  uni.showToast({
    title,
    icon,
    duration
  })
}

// 显示确认弹窗
export const showModal = (title, content, showCancel = true) => {
  return new Promise((resolve) => {
    uni.showModal({
      title,
      content,
      showCancel,
      success: (res) => {
        resolve(res.confirm)
      },
      fail: () => {
        resolve(false)
      }
    })
  })
}

// 页面跳转
export const navigateTo = (url, params = {}) => {
  const query = Object.keys(params).length > 0 
    ? '?' + Object.keys(params).map(key => `${key}=${encodeURIComponent(params[key])}`).join('&')
    : ''
  
  uni.navigateTo({
    url: url + query
  })
}

// 页面重定向
export const redirectTo = (url, params = {}) => {
  const query = Object.keys(params).length > 0 
    ? '?' + Object.keys(params).map(key => `${key}=${encodeURIComponent(params[key])}`).join('&')
    : ''
  
  uni.redirectTo({
    url: url + query
  })
}

// 返回上一页
export const navigateBack = (delta = 1) => {
  // #ifdef APP-PLUS
  uni.navigateBack({ delta })
  // #endif
  
  // #ifdef H5
  if (window.history.length > 1) {
    window.history.go(-delta)
  } else {
    window.location.href = '/'
  }
  // #endif
  
  // #ifdef MP
  uni.navigateBack({ delta })
  // #endif
}

/**
 * 处理视频云服务URL的图片处理参数
 * @param {string} url - 原始URL
 * @param {number} hSize - 图片高度，默认为480
 * @returns {string} 处理后的URL
 */
export const processVodImageUrl = (url, hSize = 480) => {
  // 如果URL不存在或不包含vod.qinsilk.com，直接返回原URL
  if (!url || typeof url !== 'string' || !url.includes('vod.qinsilk.com')) {
    return url
  }

  // 检查是否为图片文件
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.svg']
  const urlLower = url.toLowerCase()
  const isImageFile = imageExtensions.some(ext => urlLower.includes(ext))
  
  // 如果不是图片文件，直接返回原URL
  if (!isImageFile) {
    return url
  }

  // 检查是否已经包含参数
  const hasParams = url.includes('?')
  const separator = hasParams ? '&' : '?'
  
  // 拼接图片处理参数
  const processedUrl = `${url}${separator}x-oss-process=image/auto-orient,1/resize,h_${hSize}`
  
  return processedUrl
}

/**
 * 下载文件并保存到相册
 * @param {Object} options 配置选项
 * @param {string} options.downloadUrl 下载链接
 * @param {string} options.fileName 文件名
 * @param {Function} options.onProgress 进度回调函数 (progress: number) => void
 * @param {Function} options.onSuccess 成功回调函数
 * @param {Function} options.onError 错误回调函数
 * @param {Function} options.onCancel 取消回调函数
 * @returns {Object} 返回下载任务对象，包含abort方法用于取消下载
 */
export const downloadAndSaveToAlbum = (options) => {
  const {
    downloadUrl,
    fileName = '文件',
    onProgress,
    onSuccess,
    onError,
    onCancel
  } = options

  // 检查相册权限
  const checkPhotoAlbumPermission = () => {
    return new Promise((resolve) => {
      // #ifdef APP-PLUS
      // APP端检查存储权限
      try {
        if (typeof plus !== 'undefined' && plus.android) {
          const main = plus.android.runtimeMainActivity();
          const PackageManager = plus.android.importClass('android.content.pm.PackageManager');
          const permission = 'android.permission.WRITE_EXTERNAL_STORAGE';
          
          const granted = main.checkSelfPermission(permission) === PackageManager.PERMISSION_GRANTED;
          resolve(granted);
        } else {
          resolve(false);
        }
      } catch (error) {
        console.error('权限检查失败:', error);
        resolve(false);
      }
      // #endif
      
      // #ifdef H5
      // H5环境下默认有权限
      resolve(true);
      // #endif
      
      // #ifdef MP-WEIXIN
      // 微信小程序检查相册权限
      uni.getSetting({
        success: (res) => {
          if (res.authSetting['scope.writePhotosAlbum'] === false) {
            resolve(false);
          } else if (res.authSetting['scope.writePhotosAlbum'] === true) {
            resolve(true);
          } else {
            uni.authorize({
              scope: 'scope.writePhotosAlbum',
              success: () => resolve(true),
              fail: () => resolve(false)
            });
          }
        },
        fail: () => resolve(false)
      });
      // #endif
      
      // 其他平台默认有权限
      // #ifndef APP-PLUS
      // #ifndef H5
      // #ifndef MP-WEIXIN
      resolve(true);
      // #endif
      // #endif
      // #endif
    });
  }

  // 请求相册权限
  const requestPhotoAlbumPermission = () => {
    return new Promise((resolve) => {
      uni.showModal({
        title: '需要相册权限',
        content: '为了保存文件到相册，需要您授权相册访问权限。请在设置中开启"保存到相册"权限。',
        confirmText: '去设置',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            // #ifdef APP-PLUS
            // APP端直接请求权限
            if (typeof plus !== 'undefined' && plus.android) {
              const permission = 'android.permission.WRITE_EXTERNAL_STORAGE';
              plus.android.requestPermissions([permission], (resultObj) => {
                if (resultObj.granted.length > 0) {
                  uni.showToast({ title: '权限获取成功', icon: 'success' });
                  resolve(true);
                } else {
                  uni.showToast({ title: '权限获取失败', icon: 'none' });
                  resolve(false);
                }
              }, (error) => {
                uni.showToast({ title: '权限获取失败', icon: 'none' });
                resolve(false);
              });
            } else {
              uni.showToast({ title: '当前环境不支持权限请求', icon: 'none' });
              resolve(false);
            }
            // #endif
            
            // #ifdef H5
            resolve(true);
            // #endif
            
            // #ifdef MP-WEIXIN
            // 微信小程序打开设置页面
            uni.openSetting({
              success: (settingRes) => {
                if (settingRes.authSetting['scope.writePhotosAlbum'] === true) {
                  uni.showToast({ title: '权限获取成功', icon: 'success' });
                  resolve(true);
                } else {
                  uni.showToast({ title: '请在设置中开启相册权限', icon: 'none' });
                  resolve(false);
                }
              },
              fail: () => {
                uni.showToast({ title: '打开设置失败', icon: 'none' });
                resolve(false);
              }
            });
            // #endif
            
            // 其他平台默认有权限
            // #ifndef APP-PLUS
            // #ifndef H5
            // #ifndef MP-WEIXIN
            resolve(true);
            // #endif
            // #endif
            // #endif
          } else {
            resolve(false);
          }
        }
      });
    });
  }

  // 开始下载流程
  const startDownload = async () => {
    try {
      // 检查权限
      const hasPermission = await checkPhotoAlbumPermission()
      if (!hasPermission) {
        // 请求权限
        const granted = await requestPhotoAlbumPermission()
        if (!granted) {
          onError && onError(new Error('用户拒绝相册权限'))
          return null
        }
      }

      // 开始下载
      const downloadTask = uni.downloadFile({
        url: downloadUrl,
        success: (res) => {
          if (res.statusCode === 200) {
            // 保存到相册
            uni.saveVideoToPhotosAlbum({
              filePath: res.tempFilePath,
              success: () => {
                uni.showToast({ title: '已保存到相册', icon: 'success' });
                onSuccess && onSuccess(res.tempFilePath)
              },
              fail: (err) => {
                if (err.errMsg && err.errMsg.includes('auth deny')) {
                  uni.showModal({
                    title: '提示',
                    content: '需要您授权保存到相册',
                    success: (modalRes) => {
                      if (modalRes.confirm) {
                        uni.openSetting()
                      }
                    }
                  })
                } else {
                  uni.showToast({ title: '保存到相册失败', icon: 'none' });
                }
                onError && onError(err)
              }
            });
          } else {
            const error = new Error('下载失败')
            uni.showToast({ title: '下载失败', icon: 'none' });
            onError && onError(error)
          }
        },
        fail: (err) => {
          console.error('下载失败:', err)
          uni.showToast({ title: '下载失败', icon: 'none' });
          onError && onError(err)
        }
      })

      // 监听下载进度
      downloadTask.onProgressUpdate((res) => {
        onProgress && onProgress(res.progress)
      })

      return downloadTask
    } catch (error) {
      console.error('下载流程失败:', error)
      uni.showToast({ title: '网络错误', icon: 'none' });
      onError && onError(error)
      return null
    }
  }

  // 返回下载任务对象
  return startDownload()
}

export default {
  debounce,
  throttle,
  deepClone,
  formatTime,
  formatFileSize,
  generateUUID,
  validatePhone,
  validateEmail,
  validateIdCard,
  getQueryParams,
  storage,
  getDeviceInfo,
  checkPermission,
  requestPermission,
  compressImage,
  chooseImage,
  previewImage,
  showLoading,
  hideLoading,
  showToast,
  showModal,
  navigateTo,
  redirectTo,
  navigateBack,
  processVodImageUrl,
  downloadAndSaveToAlbum
} 