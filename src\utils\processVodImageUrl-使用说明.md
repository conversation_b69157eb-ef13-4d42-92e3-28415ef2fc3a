# processVodImageUrl 全局方法使用说明

## 功能描述

`processVodImageUrl` 是一个全局方法，用于处理视频云服务URL的图片处理参数。该方法会自动检测URL是否包含 `vod.qinsilk.com` 域名，并为其添加阿里云OSS图片处理参数。

## 方法签名

```javascript
processVodImageUrl(url, hSize = 480)
```

## 参数说明

- `url` (string): 原始URL地址
- `hSize` (number, 可选): 图片高度，默认为480

## 返回值

- `string`: 处理后的URL地址

## 处理逻辑

1. **URL验证**: 检查URL是否存在且包含 `vod.qinsilk.com` 字符串
2. **图片文件检查**: 检查URL是否包含图片文件扩展名（.jpg, .jpeg, .png, .gif, .webp, .bmp, .svg）
3. **参数拼接**: 如果URL已包含参数，使用 `&` 连接符；否则使用 `?` 连接符
4. **高度设置**: 使用传入的 `hSize` 参数，如果未传入则使用默认值 480
5. **返回结果**: 返回拼接了图片处理参数的完整URL

## 使用示例

### 在 Vue 组件中使用

```vue
<template>
  <view>
    <image :src="processedImageUrl" />
  </view>
</template>

<script>
export default {
  data() {
    return {
      originalUrl: 'https://vod.qinsilk.com/image/123.jpg'
    }
  },
  computed: {
    processedImageUrl() {
      // 使用默认高度480
      return this.$tools.processVodImageUrl(this.originalUrl)
    }
  }
}
</script>
```

### 在 JavaScript 中使用

```javascript
// 导入方法
import { processVodImageUrl } from '@/utils/tools.js'

// 基本使用
const url1 = 'https://vod.qinsilk.com/image/123.jpg'
const processedUrl1 = processVodImageUrl(url1)
// 结果: 'https://vod.qinsilk.com/image/123.jpg?x-oss-process=image/auto-orient,1/resize,h_480'

// 自定义高度
const url2 = 'https://vod.qinsilk.com/image/456.png'
const processedUrl2 = processVodImageUrl(url2, 720)
// 结果: 'https://vod.qinsilk.com/image/456.png?x-oss-process=image/auto-orient,1/resize,h_720'

// URL已有参数的情况
const url3 = 'https://vod.qinsilk.com/image/789.webp?param1=value1'
const processedUrl3 = processVodImageUrl(url3, 600)
// 结果: 'https://vod.qinsilk.com/image/789.webp?param1=value1&x-oss-process=image/auto-orient,1/resize,h_600'

// 非目标域名的情况
const url4 = 'https://example.com/image.jpg'
const processedUrl4 = processVodImageUrl(url4, 480)
// 结果: 'https://example.com/image.jpg' (直接返回原URL)

// 非图片文件的情况
const url5 = 'https://vod.qinsilk.com/video/123.mp4'
const processedUrl5 = processVodImageUrl(url5, 480)
// 结果: 'https://vod.qinsilk.com/video/123.mp4' (直接返回原URL，因为不是图片文件)
```

## 测试用例

### 正常情况
- ✅ URL包含 `vod.qinsilk.com` 且为图片文件，无参数，使用默认高度
- ✅ URL包含 `vod.qinsilk.com` 且为图片文件，无参数，使用自定义高度
- ✅ URL包含 `vod.qinsilk.com` 且为图片文件，已有参数，追加新参数

### 边界情况
- ✅ 空字符串URL
- ✅ null/undefined URL
- ✅ 非目标域名URL
- ✅ 非字符串类型URL
- ✅ 非图片文件类型（如.mp4, .mp3等）

## 注意事项

1. **域名限制**: 只有包含 `vod.qinsilk.com` 的URL才会被处理
2. **文件类型限制**: 只有图片文件（.jpg, .jpeg, .png, .gif, .webp, .bmp, .svg）才会被处理
3. **参数拼接**: 自动检测URL是否已有参数，选择合适的连接符
4. **高度默认值**: 未传入 `hSize` 参数时，默认使用 480
5. **错误处理**: 对无效输入进行安全处理，直接返回原URL

## 全局注册

该方法已在 `main.js` 中注册为全局方法，可通过 `this.$tools.processVodImageUrl()` 在Vue组件中直接使用。 