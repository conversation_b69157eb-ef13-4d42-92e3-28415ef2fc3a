// 阿里云VOD上传工具（本地SDK版）
// 注意：需在index.html或入口js中提前加载src/lib/aliyun-upload-sdk-1.5.6/aliyun-upload-sdk-1.5.7.min.js

/**
 * 上传视频到阿里云VOD
 * @param {File} file - 要上传的视频文件
 * @param {string} uploadAuth - 上传凭证
 * @param {string} uploadAddress - 上传地址
 * @param {Function} onProgress - 上传进度回调(percent)
 * @returns {Promise<string>} resolve(videoId)
 */
export function uploadToAliyunVOD({ file, uploadAuth, uploadAddress, onProgress }) {
  return new Promise((resolve, reject) => {
    // 检查SDK是否正确加载
    console.log('检查AliyunUpload SDK:', {
      window: typeof window,
      AliyunUpload: typeof window.AliyunUpload,
      AliyunUploadVod: window.AliyunUpload ? typeof window.AliyunUpload.Vod : 'undefined'
    });
    
    if (!window.AliyunUpload) {
      const error = 'AliyunUpload SDK未加载，请检查index.html中的引入路径';
      console.error(error);
      return reject(new Error(error));
    }
    
    if (!window.AliyunUpload.Vod) {
      const error = 'AliyunUpload.Vod构造函数不存在，请检查SDK版本';
      console.error(error);
      return reject(new Error(error));
    }
    
    try {
      const uploader = new window.AliyunUpload.Vod({
        onUploadstarted: (uploadInfo) => {
          console.log('上传开始:', uploadInfo);
          uploader.setUploadAuthAndAddress(uploadInfo, uploadAuth, uploadAddress);
        },
        onUploadprogress: (uploadInfo, totalSize, loadedPercent) => {
          const percent = Math.floor(loadedPercent * 100);
          console.log(`上传进度: ${percent}%`);
          if (onProgress) onProgress(percent);
        },
        onUploadSucceed: (uploadInfo) => {
          console.log('上传成功:', uploadInfo);
          resolve(uploadInfo.videoId);
        },
        onUploadFailed: (uploadInfo, code, message) => {
          console.error('上传失败:', { uploadInfo, code, message });
          reject(new Error(message || `上传失败，错误代码: ${code}`));
        }
      });
      
      console.log('准备添加文件:', file);
      uploader.addFile(file, null, null, null, undefined);
      console.log('开始上传');
      uploader.startUpload();
    } catch (error) {
      console.error('创建上传器失败:', error);
      reject(new Error(`创建上传器失败: ${error.message}`));
    }
  });
}
