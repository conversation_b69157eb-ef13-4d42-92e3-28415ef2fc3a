# 文案生成服务使用说明

## 概述

文案生成服务 (`src/service/docGenerator.js`) 是一个公共的文案生成工具，实现了以下功能：

1. **登录后预生成文案**：用户登录后自动生成默认文案并保存到本地存储
2. **智能加载策略**：进入创建页面时优先使用本地存储的文案，避免重复请求
3. **并发控制**：当有文案正在生成时，其他请求会等待生成完成
4. **本地存储同步**：文案数据修改后自动同步到本地存储
5. **统一错误处理**：统一的loading弹窗和错误提示，支持遮罩层防止背景点击

## 主要功能

### 1. 预生成文案（无loading弹窗）

```javascript
import docGeneratorService from '@/service/docGenerator'

// 登录后调用，不显示loading弹窗
await docGeneratorService.generateDefaultDocs({
  function_type: 1,
  prompt_type: 'default',
  custom_requirements: '请生成一条适合短视频的吸引人的文案'
})
```

### 2. 带loading弹窗的文案生成

```javascript
// 正常生成流程，会显示带遮罩的loading弹窗
const docs = await docGeneratorService.generateDocsWithLoading({
  function_type: 1,
  prompt_type: 'custom',
  custom_requirements: '自定义要求',
  loadingTitle: '文案生成中...'
})
```

### 3. 获取本地存储的文案

```javascript
// 获取本地存储的预生成文案
const storedDocs = docGeneratorService.getStoredDocs()
```

### 4. 检查生成状态

```javascript
// 检查是否正在生成文案
const isGenerating = docGeneratorService.isGenerating()
```

### 5. 保存文案到本地存储

```javascript
// 手动保存文案数据到本地存储
docGeneratorService.saveStoredDocs(docs)
```

## 使用场景

### 场景1：登录后预生成

在用户登录成功后，自动生成一条默认文案：

```javascript
// 在 src/store/modules/user.js 的 initAfterLogin 方法中
const initAfterLogin = async () => {
  try {
    await docGeneratorService.generateDefaultDocs()
    console.log('登录后预生成文案完成')
  } catch (error) {
    console.error('登录后预生成文案失败:', error)
  }
}
```

### 场景2：进入创建页面

在 `src/pages/create/index.vue` 的 `generateDefaultDoc` 方法中：

```javascript
async generateDefaultDoc() {
  // 先尝试从本地存储获取预生成的文案
  const storedDocs = docGeneratorService.getStoredDocs()
  if (storedDocs.length > 0) {
    // 使用本地存储的文案，支持多项文案显示
    const validDocs = storedDocs.filter(doc => doc.content)
    if (validDocs.length > 0) {
      // 更新文案列表
      this.videoDocList = validDocs
      // 合并所有文案内容
      this.docContent = validDocs.map(doc => doc.content).join('\n\n')
      return
    }
  }
  
  // 如果没有本地存储的文案，则生成新的文案
  const docs = await docGeneratorService.generateDocsWithLoading({
    function_type: 1,
    prompt_type: 'default',
    custom_requirements: '请生成一条适合短视频的吸引人的文案'
  })
  
  if (docs.length > 0) {
    this.videoDocList = docs
    this.docContent = docs.map(doc => doc.content).join('\n\n')
  }
}
```

### 场景3：用户主动生成文案

在文案对话框中生成新文案：

```javascript
async onVideoDocAdd(payload) {
  const idx = this.videoDocList.length
  this.videoDocList.push({ content: '' })
  
  try {
    const docs = await docGeneratorService.generateDocsWithLoading({
      function_type: 1,
      prompt_type: payload.docType,
      custom_requirements: payload.customRequest,
      loadingTitle: '文案生成中...'
    })
    
    if (docs.length > 0) {
      this.$set(this.videoDocList, idx, docs[0])
      this.videoDocList = [...this.videoDocList]
      // 同步到本地存储
      docGeneratorService.saveStoredDocs(this.videoDocList)
      // 更新合并的文案内容
      this.docContent = this.videoDocList.map(doc => doc.content).join('\n\n')
    }
  } catch (error) {
    console.error('生成文案失败:', error)
    this.videoDocList.splice(idx, 1)
  }
}
```

### 场景4：文案数据修改同步

当文案数据发生变化时，自动同步到本地存储：

```javascript
// 重新生成文案
async onVideoDocRegenerate(idx) {
  try {
    const docs = await docGeneratorService.generateDocsWithLoading({
      function_type: 1,
      prompt_type: 'regenerate',
      custom_requirements: '重新生成文案'
    })
    
    if (docs.length > 0) {
      this.$set(this.videoDocList, idx, docs[0])
      this.videoDocList = [...this.videoDocList]
      // 同步到本地存储
      docGeneratorService.saveStoredDocs(this.videoDocList)
      // 更新合并的文案内容
      this.docContent = this.videoDocList.map(doc => doc.content).join('\n\n')
    }
  } catch (error) {
    console.error('重新生成文案失败:', error)
  }
}

// 复制并添加文案
async onVideoDocCopyAdd(idx) {
  const originalDoc = this.videoDocList[idx]
  if (originalDoc) {
    const newDoc = { ...originalDoc, content: originalDoc.content + ' (副本)' }
    this.videoDocList.push(newDoc)
    this.videoDocList = [...this.videoDocList]
    // 同步到本地存储
    docGeneratorService.saveStoredDocs(this.videoDocList)
    // 更新合并的文案内容
    this.docContent = this.videoDocList.map(doc => doc.content).join('\n\n')
  }
}

// 文案列表变化
onVideoDocListChange(list) {
  this.videoDocList = list
  // 同步到本地存储
  docGeneratorService.saveStoredDocs(this.videoDocList)
  // 更新合并的文案内容
  this.docContent = this.videoDocList.map(doc => doc.content).join('\n\n')
}
```

## 并发控制机制

当有文案正在生成时，其他请求会等待生成完成：

1. **设置生成标志**：开始生成时设置 `isGeneratingDocs` 标志
2. **检查生成状态**：新请求检查是否正在生成
3. **等待完成**：如果正在生成，显示"等待文案生成完成..."的loading（带遮罩）
4. **返回结果**：等待完成后返回本地存储的文案

## 本地存储

- **键名**：`preGeneratedDocs` - 存储预生成的文案数组
- **键名**：`isGeneratingDocs` - 存储生成状态标志

## Loading弹窗配置

所有 `uni.showLoading` 调用都配置了 `mask: true` 属性：

```javascript
uni.showLoading({
  title: '文案生成中...',
  mask: true  // 显示遮罩层，防止背景点击
})
```

这确保了：
- **阻塞交互**：loading期间用户无法点击背景
- **最高层级**：遮罩层显示在最顶层
- **统一体验**：所有loading状态都有相同的交互行为

## 错误处理

- 所有方法都包含 try-catch 错误处理
- 生成失败时会显示友好的错误提示
- 不会影响应用的正常运行
- 确保loading状态正确关闭

## 注意事项

1. **存储格式**：本地存储的文案是数组格式 `[{ content: '文案内容' }]`
2. **多项显示**：支持显示多个文案项目，内容通过换行符连接
3. **异步处理**：所有生成方法都是异步的，需要使用 `await` 或 `.then()`
4. **状态管理**：生成状态通过本地存储管理，确保跨页面状态同步
5. **性能优化**：优先使用本地存储的文案，减少不必要的网络请求
6. **数据同步**：文案数据修改后会自动同步到本地存储
7. **遮罩层**：所有loading弹窗都配置了遮罩层，防止用户误操作

## 更新日志

### v1.1.0
- 新增 `saveStoredDocs` 方法，支持手动保存文案数据
- 完善多项文案显示功能，支持多个文案项目
- 所有 `uni.showLoading` 调用添加 `mask: true` 属性
- 优化文案内容合并逻辑，使用换行符分隔多个文案
- 增强错误处理和状态管理 