# CityPicker 城市选择器组件

## 概述

CityPicker 是一个支持省市区三级联动的城市选择器组件，采用底部弹窗的交互方式，支持灵活的显示文字配置。

## 特性

- ✅ 三级联动选择（省份 → 城市 → 区县）
- ✅ 底部弹窗交互，符合移动端使用习惯
- ✅ 深色主题适配
- ✅ 灵活的显示文字配置
- ✅ 完整的数据返回
- ✅ 支持数据回显
- ✅ 支持取消/确认操作

## API

### Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| modelValue | Array | [] | 选中的值数组，格式：[provinceId, cityId, districtId] |
| placeholder | String | '请选择城市' | 占位符文本 |
| levels | Array | ['province', 'city', 'district'] | 控制显示文字的级别配置 |

### levels 配置说明

`levels` 属性用于控制选中后显示的文字内容，可选值：
- `'province'` - 省份
- `'city'` - 城市  
- `'district'` - 区县

**重要**：弹窗中始终显示三列选择器，`levels` 只影响显示文字。

### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:modelValue | Array | 选中值变化时触发 |
| change | Object | 选中值变化时触发，包含详细信息 |

### change 事件参数

```javascript
{
  value: [110000, 110100, 110101],  // 选中的ID数组
  text: "北京市 东城区",              // 显示文字（根据levels配置）
  province: { id: 110000, areaName: "北京市" },
  city: { id: 110100, areaName: "北京市" },
  district: { id: 110101, areaName: "东城区" }
}
```

## 使用示例

### 基础用法

```vue
<template>
  <CityPicker 
    v-model="cityArray" 
    placeholder="请选择城市"
    @change="onCityChange"
  />
</template>

<script setup>
import { ref } from 'vue'
import CityPicker from './CityPicker.vue'

const cityArray = ref([])

const onCityChange = (e) => {
  console.log('选中的城市:', e)
  // e.value: [110000, 110100, 110101]
  // e.text: "北京市 北京市 东城区"
}
</script>
```

### 自定义显示文字

```vue
<template>
  <!-- 只显示城市和区县 -->
  <CityPicker 
    v-model="cityArray" 
    :levels="['city', 'district']"
    placeholder="请选择城市"
    @change="onCityChange"
  />
  
  <!-- 只显示省份 -->
  <CityPicker 
    v-model="provinceArray" 
    :levels="['province']"
    placeholder="请选择省份"
    @change="onProvinceChange"
  />
</template>
```

## 核心设计理念

### 弹窗显示
- 始终显示三列（省、市、区），不受props影响
- 保持一致的用户交互体验
- 支持完整的三级联动选择

### 数据返回
- 始终返回完整的三级数据 `[provinceId, cityId, districtId]`
- 确保后端能获得完整的地址信息
- 便于数据处理和统计分析

### 文字显示控制
- `levels` 属性只控制显示哪些级别的文字
- 根据业务需求灵活配置显示内容
- 避免冗余信息，提升用户体验

## 实际应用场景

### 场景1：门店地址选择
```vue
<!-- 用户需要选择完整地址，但只想显示城市和区县 -->
<CityPicker 
  v-model="formData.cityArray" 
  :levels="['city', 'district']"
  placeholder="请选择城市"
  @change="onCityChange"
/>
```

**效果**：
- 用户体验：显示"北京市 东城区"，简洁明了
- 数据完整性：后端获得完整的省市区ID `[110000, 110100, 110101]`

### 场景2：省份统计
```vue
<!-- 用户选择省份进行数据统计，但需要完整的地址信息 -->
<CityPicker 
  v-model="formData.regionArray" 
  :levels="['province']"
  placeholder="请选择省份"
  @change="onRegionChange"
/>
```

**效果**：
- 用户体验：只显示"北京市"，符合业务需求
- 数据完整性：后端获得完整的省市区ID，便于精确统计

## 数据格式

### 输入数据（aiArea.json）

```json
[
  {
    "areaName": "北京市",
    "level": 1,
    "pid": 0,
    "id": 110000,
    "children": [
      {
        "areaName": "北京市",
        "level": 2,
        "pid": 110000,
        "id": 110100,
        "children": [
          {
            "areaName": "东城区",
            "level": 3,
            "pid": 110100,
            "id": 110101
          }
        ]
      }
    ]
  }
]
```

### 输出数据格式

无论 `levels` 如何配置，组件始终返回完整的三级数据：

```javascript
// modelValue 格式
[110000, 110100, 110101]  // [省份ID, 城市ID, 区县ID]

// change 事件数据格式
{
  value: [110000, 110100, 110101],
  text: "根据levels配置生成的文字",
  province: { id: 110000, areaName: "北京市" },
  city: { id: 110100, areaName: "北京市" },
  district: { id: 110101, areaName: "东城区" }
}
```

## 样式说明

### 触发器样式

组件的触发器采用右对齐布局，与原有设计保持一致：

```css
.ai-setting-city-display {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 8rpx 20rpx 8rpx 0;
  min-height: 44rpx;
  background-color: transparent;
}
```

### 弹窗样式

弹窗采用深色主题，与整体UI风格保持一致：

```css
.city-picker-content {
  background-color: #1c1c1e;
  border-radius: 24rpx 24rpx 0 0;
}

.city-picker-view {
  background-color: #1c1c1e !important;
}
```

## 技术实现

### 文字生成逻辑
```javascript
// 根据levels配置生成显示文字
let textParts = []
if (props.levels.includes('province')) {
  textParts.push(province.areaName)
}
if (props.levels.includes('city')) {
  textParts.push(city.areaName)
}
if (props.levels.includes('district')) {
  textParts.push(district.areaName)
}
return textParts.join(' ')
```

### 数据验证
```javascript
// 始终验证完整的三级数据
if (props.modelValue && props.modelValue.length >= 3) {
  const [provinceId, cityId, districtId] = props.modelValue
  // 处理逻辑...
}
```

### 深色主题适配
```vue
<!-- 强制设置picker-view背景色 -->
<picker-view 
  :style="{ backgroundColor: '#1c1c1e' }"
>
  <picker-view-column :style="{ backgroundColor: '#1c1c1e' }">
    <view :style="{ backgroundColor: '#1c1c1e', color: '#ffffff' }">
      {{ item.areaName }}
    </view>
  </picker-view-column>
</picker-view>
```

## 注意事项

1. **数据完整性**：组件始终返回完整的三级数据，确保后端能获得完整的地址信息
2. **显示控制**：`levels` 属性只控制显示文字，不影响弹窗的列数和数据返回
3. **级联更新**：选择省份后城市列表自动更新，选择城市后区县列表自动更新
4. **数据回显**：组件会根据传入的 modelValue 自动回显选中状态
5. **深色主题**：组件已适配深色主题，包括picker-view的背景色

## 常见问题

### Q: 为什么弹窗始终显示三列？
A: 为了保持一致的用户体验，弹窗始终显示省市区三列。`levels` 属性只控制选中后的显示文字。

### Q: 如何获取完整的地址信息？
A: 组件始终返回完整的三级数据，可以通过 change 事件的 province、city、district 属性获取详细信息。

### Q: 如何自定义显示文字？
A: 通过配置 `levels` 属性来控制显示哪些级别的文字，如 `['city', 'district']` 只显示城市和区县。

## 更新日志

### v1.2.0
- 优化数据返回格式，始终返回完整三级数据
- 改进用户体验，弹窗固定显示三列
- 完善文档和使用示例

### v1.1.0
- 添加 `levels` 属性支持灵活的显示文字配置
- 优化深色主题适配
- 修复picker-view背景色问题

### v1.0.0
- 初始版本，支持基础的三级联动选择
