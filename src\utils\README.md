# 工具类和服务使用说明

## 概述

本项目提供了完整的HTTP请求工具类和接口服务层，包含：

- **HTTP工具类** (`src/utils/http.js`) - 基于axios的HTTP请求封装
- **通用工具类** (`src/utils/tools.js`) - 常用工具函数
- **接口服务层** (`src/service/`) - 按模块分类的接口服务
- **使用示例** (`src/service/example.js`) - 详细的使用示例

## 目录结构

```
src/
├── utils/
│   ├── http.js          # HTTP请求工具类
│   ├── tools.js         # 通用工具函数
│   └── README.md        # 使用说明
└── service/
    ├── index.js         # 服务统一导出
    ├── user.js          # 用户相关接口
    ├── video.js         # 视频相关接口
    ├── material.js      # 素材相关接口
    ├── system.js        # 系统相关接口
    └── example.js       # 使用示例
```

## HTTP工具类使用

### 基本用法

```javascript
import http from '@/utils/http'

// GET请求
const result = await http.get('/api/users', { page: 1, size: 10 })

// POST请求
const result = await http.post('/api/users', { name: 'John', email: '<EMAIL>' })

// PUT请求
const result = await http.put('/api/users/123', { name: 'Jane' })

// DELETE请求
const result = await http.delete('/api/users/123')

// 文件上传
const result = await http.upload('/api/upload', filePath, { type: 'image' })

// 文件下载
const downloadTask = await http.download('/api/download/123')
```

### 特性

- ✅ **自动token管理** - 自动添加Authorization头
- ✅ **请求/响应拦截** - 统一处理请求头和响应数据
- ✅ **错误处理** - 统一的错误处理和用户提示
- ✅ **日志记录** - 详细的请求日志和错误日志
- ✅ **设备信息** - 自动添加设备信息到请求头
- ✅ **请求追踪** - 每个请求都有唯一ID用于追踪
- ✅ **文件上传/下载** - 支持文件上传和下载功能

### 配置

```javascript
// 环境配置在 src/utils/http.js 中
const config = {
  baseURL: process.env.NODE_ENV === 'production' 
    ? 'https://api.wangcut.com' 
    : 'https://dev-api.wangcut.com',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json;charset=UTF-8'
  }
}
```

## 通用工具类使用

### 常用函数

```javascript
import { 
  debounce, 
  throttle, 
  formatTime, 
  validatePhone, 
  storage,
  showToast,
  showLoading
} from '@/utils/tools'

// 防抖
const debouncedFn = debounce(() => {
  console.log('执行搜索')
}, 300)

// 节流
const throttledFn = throttle(() => {
  console.log('滚动处理')
}, 100)

// 时间格式化
const formatted = formatTime(Date.now(), 'YYYY-MM-DD HH:mm:ss')

// 手机号验证
const isValid = validatePhone('13800138000')

// 存储操作
storage.set('key', 'value', 3600) // 1小时过期
const value = storage.get('key')
storage.remove('key')

// UI提示
showToast('操作成功', 'success')
showLoading('加载中...')
```

## 接口服务使用

### 导入方式

```javascript
// 方式1：按需导入
import { userService, videoService } from '@/service'

// 方式2：全量导入
import services from '@/service'
const { user, video } = services

// 方式3：单独导入
import userService from '@/service/user'
```

### 用户服务示例

```javascript
import { userService } from '@/service'

// 用户登录
const loginResult = await userService.login({
  phone: '13800138000',
  password: '123456'
})

// 获取用户信息
const userInfo = await userService.getUserInfo()

// 上传头像
const avatarResult = await userService.uploadAvatar(filePath)

// 更新用户信息
const updateResult = await userService.updateUserInfo({
  nickname: '新昵称',
  avatar: 'https://example.com/avatar.jpg'
})
```

### 视频服务示例

```javascript
import { videoService } from '@/service'

// 获取视频列表
const videoList = await videoService.getVideoList({
  page: 1,
  pageSize: 10,
  category: 'music'
})

// 创建视频项目
const project = await videoService.createVideoProject({
  name: '我的视频项目',
  description: '项目描述',
  templateId: 'template_123'
})

// AI视频生成
const aiTask = await videoService.generateAIVideo({
  prompt: '生成一个关于旅行的视频',
  duration: 30,
  style: 'cinematic'
})

// 上传视频
const uploadResult = await videoService.uploadVideo(filePath, {
  title: '视频标题',
  description: '视频描述'
})
```

### 素材服务示例

```javascript
import { materialService } from '@/service'

// 获取素材列表
const materials = await materialService.getMaterialList({
  type: 'image',
  category: 'nature',
  page: 1,
  pageSize: 20
})

// 上传素材
const uploadResult = await materialService.uploadMaterial(filePath, {
  name: '素材名称',
  description: '素材描述',
  category: 'nature',
  tags: ['风景', '自然']
})

// 下载素材
const downloadTask = await materialService.downloadMaterial('material_123')

// 收藏素材
await materialService.favoriteMaterial('material_123')
```

### 系统服务示例

```javascript
import { systemService } from '@/service'

// 检查应用更新
const updateInfo = await systemService.checkAppUpdate()

// 获取系统配置
const config = await systemService.getSystemConfig()

// 提交反馈
const feedbackResult = await systemService.submitFeedback({
  type: 'bug',
  title: '问题标题',
  content: '问题描述',
  contact: '<EMAIL>'
})

// 获取帮助文档
const helpDocs = await systemService.getHelpDocs({
  category: 'usage',
  page: 1
})
```

## 错误处理

### 全局错误处理

HTTP工具类已经内置了全局错误处理：

- **401未授权** - 自动清除token并跳转登录页
- **403无权限** - 显示权限不足提示
- **404资源不存在** - 显示资源不存在提示
- **500服务器错误** - 显示服务器错误提示
- **网络错误** - 显示网络连接失败提示
- **超时错误** - 显示请求超时提示

### 自定义错误处理

```javascript
try {
  const result = await userService.login(loginData)
  // 处理成功结果
} catch (error) {
  // 自定义错误处理
  if (error.code === 'INVALID_CREDENTIALS') {
    showToast('用户名或密码错误')
  } else if (error.code === 'ACCOUNT_LOCKED') {
    showToast('账号已被锁定')
  } else {
    showToast(error.message || '登录失败')
  }
}
```

## 日志系统

### 日志类型

- **request** - 请求日志
- **response** - 响应日志
- **request_error** - 请求错误日志
- **response_error** - 响应错误日志
- **upload_start** - 上传开始日志
- **upload_success** - 上传成功日志
- **upload_error** - 上传错误日志
- **download_start** - 下载开始日志
- **download_success** - 下载成功日志
- **download_error** - 下载错误日志

### 日志上传配置

```javascript
// 在 src/utils/http.js 中配置实际的日志上传地址
const uploadLog = (logData) => {
  // 替换为实际的日志服务地址
  const logUrl = 'https://log.wangcut.com/api/logs'
  
  axios.post(logUrl, {
    ...logData,
    timestamp: new Date().toISOString(),
    userAgent: navigator.userAgent,
    platform: uni.getSystemInfoSync().platform
  }).catch(err => {
    console.error('日志上传失败:', err)
  })
}
```

## 最佳实践

### 1. 使用async/await

```javascript
// 推荐
try {
  const result = await userService.login(data)
  // 处理结果
} catch (error) {
  // 处理错误
}

// 不推荐
userService.login(data).then(result => {
  // 处理结果
}).catch(error => {
  // 处理错误
})
```

### 2. 统一错误处理

```javascript
// 创建通用的错误处理函数
const handleApiError = (error, defaultMessage = '操作失败') => {
  const message = error.message || defaultMessage
  showToast(message)
  console.error('API错误:', error)
}

// 使用
try {
  await userService.updateUserInfo(data)
  showToast('更新成功', 'success')
} catch (error) {
  handleApiError(error, '更新失败')
}
```

### 3. 加载状态管理

```javascript
const handleWithLoading = async (asyncFn, loadingText = '加载中...') => {
  try {
    showLoading(loadingText)
    const result = await asyncFn()
    return result
  } finally {
    hideLoading()
  }
}

// 使用
const result = await handleWithLoading(
  () => videoService.getVideoList({ page: 1 }),
  '获取视频列表中...'
)
```

### 4. 请求重试

```javascript
// 使用示例中的重试函数
import { errorHandlingExamples } from '@/service/example'

const result = await errorHandlingExamples.requestWithRetry(
  () => systemService.getSystemConfig(),
  3, // 最大重试次数
  1000 // 重试间隔
)
```

## 注意事项

1. **环境配置** - 确保在不同环境中配置正确的API地址
2. **Token管理** - token会自动从本地存储中获取并添加到请求头
3. **错误处理** - 全局错误处理会自动显示错误提示，无需手动处理
4. **日志上传** - 目前日志只在控制台输出，需要配置实际的日志服务地址
5. **文件上传** - 支持进度监听和任务取消
6. **网络检查** - 建议在重要操作前检查网络状态

## 扩展

### 添加新的服务模块

1. 在 `src/service/` 目录下创建新的服务文件
2. 按照现有模式定义接口方法
3. 在 `src/service/index.js` 中导出新服务
4. 在 `src/service/example.js` 中添加使用示例

### 添加新的工具函数

1. 在 `src/utils/tools.js` 中添加新函数
2. 确保函数有适当的错误处理
3. 在默认导出中包含新函数
4. 添加使用示例和文档

### 自定义HTTP配置

```javascript
// 可以在 src/utils/http.js 中修改配置
const config = {
  baseURL: 'your-api-url',
  timeout: 15000,
  headers: {
    'Custom-Header': 'value'
  }
}
``` 

# Utils 工具函数说明

## address-parse.js 地址解析工具

### 功能概述
智能地址解析工具，将包含姓名、电话、地址的文本自动拆解为结构化数据。

### 主要功能
1. **智能地址解析** - 自动识别省市区、详细地址、姓名、电话等信息
2. **剪贴板获取** - 通过 `get()` 函数获取剪贴板内容并解析
3. **自定义地址数据** - 通过 `SetAddressList()` 重新设置地区数据

### 使用方法

#### 1. 获取剪贴板内容并解析（推荐）
```javascript
// ES6 导入方式
import addressParse from '@/utils/address-parse.js';

// 获取剪贴板内容并自动解析
addressParse.get().then(result => {
    console.log('解析结果：', result);
    // result 返回结构：
    // {
    //   area: "源城区",
    //   areaCode: 441602,
    //   city: "河源市",
    //   cityCode: 441600,
    //   detail: "南板桥11巷8号",
    //   name: "陈杰凌",
    //   phone: "13510358539",
    //   postalCode: "",
    //   province: "广东省",
    //   provinceCode: 440000
    // }
}).catch(error => {
    console.error('获取失败：', error);
});
```

#### 2. 重新设置地区数据
```javascript
// ES6 导入方式
import addressParse from '@/utils/address-parse.js';

// 自定义地区数据
const customAddressData = [
    {
        "areaName": "北京市",
        "level": 1,
        "pid": 0,
        "id": 110000,
        "children": [
            {
                "areaName": "北京市",
                "level": 2,
                "pid": 110000,
                "id": 110100,
                "children": [
                    {
                        "areaName": "东城区",
                        "level": 3,
                        "pid": 110100,
                        "id": 110101
                    }
                ]
            }
        ]
    }
];

// 重新设置地区数据
addressParse.SetAddressList(customAddressData);

// 然后使用 AddressParse 解析
const result = addressParse.AddressParse("张三 北京市东城区 13800138000", 1);
```

#### 3. 直接解析地址文本
```javascript
// ES6 导入方式
import addressParse from '@/utils/address-parse.js';

// 解析地址
const result = addressParse.AddressParse("张三 广东省深圳市南山区科技园 13800138000", 1);
```

### API 说明

#### get()
- **功能**：获取剪贴板内容并自动解析
- **返回**：Promise 对象，解析成功返回结构化地址数据
- **环境**：仅在 uni-app 的 App 环境中有效

#### SetAddressList(addressJson)
- **功能**：重新设置地区数据
- **参数**：addressJson - 地区数据数组
- **使用**：需要更新地区数据时调用

#### AddressParse(address, options)
- **功能**：解析地址文本
- **参数**：
  - address: 要解析的地址文本
  - options: 解析选项（可选）

### 返回数据结构
```javascript
{
    area: "区县名称",
    areaCode: "区县代码",
    city: "城市名称",
    cityCode: "城市代码",
    detail: "详细地址",
    name: "姓名",
    phone: "电话号码",
    postalCode: "邮政编码",
    province: "省份名称",
    provinceCode: "省份代码"
}
```

### 注意事项
1. **get() 函数**：自动获取剪贴板内容并解析，返回完整的地址结构
2. **地区数据**：如需更新地区数据，先调用 `SetAddressList()` 再使用 `AddressParse()`
3. **环境限制**：剪贴板功能仅在 uni-app 的 App 环境中有效 