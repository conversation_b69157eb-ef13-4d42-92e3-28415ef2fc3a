<template>
  <view class="power-details-page">
    <!-- 顶部导航栏 -->
    <view class="power-details-header">
      <view class="power-details-navbar">
        <view class="power-details-navbar-left" @click="handleBack">
          <image class="power-details-navbar-back" src="@/asset/img/team/icon_back.png" />
        </view>
        <text class="power-details-navbar-title">算力明细</text>
        <view class="power-details-navbar-right" @click="handleShowConsumptionModal">
          <text class="power-details-navbar-consumption">已使用</text>
        </view>
      </view>
    </view>

    <!-- 固定内容区域 -->
    <view class="power-details-fixed-content">
      <!-- 算力概览卡片 -->
      <view class="power-details-overview">
        <view class="power-details-overview-left" @click="handleTeamAvailablePower">
          <text class="power-details-overview-value">{{ teamAvailablePower }}</text>
          <view class="power-details-overview-label">
            <text class="power-details-overview-text">可用算力</text>
            <image class="power-details-overview-arrow" src="@/asset/img/team/icon_arrow_right.png" />
          </view>
        </view>
        <view class="power-details-overview-right">
          <text class="power-details-overview-value">{{ totalConsumption }}</text>
          <text class="power-details-overview-text">已使用算力</text>
        </view>
      </view>

      <!-- 标签页导航 -->
      <view class="power-details-tabs">
        <view
          class="power-details-tab-item"
          @click="handleTabChange('consumption')"
        >
          <text
            class="power-details-tab-text"
            :style="{
              color: activeTab === 'consumption' ? '#000' : '#999',
              fontWeight: activeTab === 'consumption' ? '500' : '400'
            }"
          >消耗明细</text>
          <view v-if="activeTab === 'consumption'" class="power-details-tab-indicator"></view>
        </view>
        <view
          class="power-details-tab-item"
          @click="handleTabChange('allocation')"
        >
          <text
            class="power-details-tab-text"
            :style="{
              color: activeTab === 'allocation' ? '#000' : '#999',
              fontWeight: activeTab === 'allocation' ? '500' : '400'
            }"
          >分配明细</text>
          <view v-if="activeTab === 'allocation'" class="power-details-tab-indicator"></view>
        </view>
      </view>
    </view>

    <!-- 滚动内容区域 -->
    <scroll-view
      class="power-details-scroll-content"
      scroll-y="true"
      @scrolltolower="handleLoadMore"
    >
      <!-- 内容展示区域 -->
      <view class="power-details-content-area">
        <!-- 加载状态 -->
        <view v-if="loading && ((activeTab === 'consumption' && consumptionList.length === 0) || (activeTab === 'allocation' && allocationList.length === 0))" class="power-details-loading">
          <text class="power-details-loading-text">正在加载算力信息...</text>
        </view>

        <!-- 消耗明细内容 -->
        <view v-else-if="activeTab === 'consumption'" class="power-details-consumption">
          <view v-if="consumptionList.length === 0" class="power-details-empty">
            <view class="power-details-empty-icon">
              <view class="power-details-empty-folder power-details-empty-folder-back"></view>
              <view class="power-details-empty-folder power-details-empty-folder-front"></view>
              <view class="power-details-empty-sparkle power-details-empty-sparkle-1"></view>
              <view class="power-details-empty-sparkle power-details-empty-sparkle-2"></view>
              <view class="power-details-empty-sparkle power-details-empty-sparkle-3"></view>
            </view>
            <text class="power-details-empty-text">暂无消耗明细</text>
            <text class="power-details-empty-emoji">🙃</text>
          </view>
          <view v-else class="power-details-list">
            <!-- 消耗明细列表 -->
            <view
              v-for="item in consumptionList"
              :key="item.id"
              class="power-details-list-item"
            >
                                            <view class="power-details-list-left">
                 <view class="power-details-list-info">
                   <text class="power-details-list-title">{{ item.title }}</text>
                   <text class="power-details-list-time">{{ item.time }}</text>
                   <text v-if="item.remark && item.status === 'rollback'" class="power-details-list-remark power-details-list-remark-rollback">{{ item.remark }}</text>
                 </view>
               </view>
               <view class="power-details-list-right">
                 <text class="power-details-list-power power-details-list-power-consumption">-{{ item.power }}</text>
                 <text class="power-details-list-status" :class="'power-details-list-status-' + (item.status || 'pending')">
                   {{ item.status === 'success' ? '成功' : item.status === 'failed' ? '失败' : item.status === 'rollback' ? '已撤回' : '处理中' }}
                 </text>
               </view>
            </view>
          </view>
        </view>

        <!-- 分配明细内容 -->
        <view v-else-if="activeTab === 'allocation'" class="power-details-allocation">
          <view v-if="allocationList.length === 0" class="power-details-empty">
            <view class="power-details-empty-icon">
              <view class="power-details-empty-folder power-details-empty-folder-back"></view>
              <view class="power-details-empty-folder power-details-empty-folder-front"></view>
              <view class="power-details-empty-sparkle power-details-empty-sparkle-1"></view>
              <view class="power-details-empty-sparkle power-details-empty-sparkle-2"></view>
              <view class="power-details-empty-sparkle power-details-empty-sparkle-3"></view>
            </view>
            <text class="power-details-empty-text">暂无分配明细</text>
            <text class="power-details-empty-emoji">🙃</text>
          </view>
          <view v-else class="power-details-list">
            <!-- 分配明细列表 -->
            <view
              v-for="item in allocationList"
              :key="item.id"
              class="power-details-list-item"
            >
                                            <view class="power-details-list-left">
                 <view class="power-details-list-info">
                   <text class="power-details-list-title">{{ item.title }}</text>
                   <text class="power-details-list-time">{{ item.time }}</text>
                   <text v-if="item.remark && item.status === 'rollback'" class="power-details-list-remark power-details-list-remark-rollback">{{ item.remark }}</text>
                 </view>
               </view>
               <view class="power-details-list-right">
                 <text class="power-details-list-power power-details-list-power-allocation">+{{ item.power }}</text>
                 <text class="power-details-list-status" :class="'power-details-list-status-' + (item.status || 'pending')">
                   {{ item.status === 'success' ? '成功' : item.status === 'failed' ? '失败' : item.status === 'rollback' ? '已撤回' : '处理中' }}
                 </text>
               </view>
            </view>
          </view>
        </view>

        <!-- 加载更多提示 -->
        <view v-if="(activeTab === 'consumption' && consumptionList.length > 0) || (activeTab === 'allocation' && allocationList.length > 0)" class="power-details-load-more">
          <view v-if="loading" class="power-details-load-more-loading">
            <text class="power-details-load-more-text">加载中...</text>
          </view>
          <view v-else-if="!hasMoreData" class="power-details-load-more-end">
            <text class="power-details-load-more-text">没有更多数据了</text>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 累计消耗弹窗 -->
    <view v-if="showConsumptionModal" class="power-details-modal">
      <view class="power-details-overlay" @click="handleCloseModal"></view>
      <view class="power-details-modal-content">
        <text class="power-details-modal-title">团队已使用算力</text>
        <text class="power-details-modal-content-text">当前已使用 {{ totalConsumption }} 算力</text>
        <view class="power-details-modal-button" @click="handleCloseModal">
          <text class="power-details-modal-button-text">我知道了</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { teamService } from '@/service'
import { getCid, getUserId } from '@/utils/http'

// 响应式数据
const activeTab = ref('consumption')
const showConsumptionModal = ref(false)
const loading = ref(false)
const isTabChanging = ref(false) // 标签页切换状态

// 算力数据
const teamRemainingPower = ref(0)
const teamAvailablePower = ref(0)
const totalConsumption = ref(0)

// 团队算力数据
const teamComputePowerData = ref(null)

// 消耗明细列表
const consumptionList = ref([])

// 分配明细列表
const allocationList = ref([])

// 分页数据
const currentPage = ref(1)
const pageSize = ref(20)
const hasMoreData = ref(true)

// 返回上一页
const handleBack = () => {
  uni.navigateBack()
}

// 显示累计消耗弹窗
const handleShowConsumptionModal = () => {
  showConsumptionModal.value = true
}

// 关闭弹窗
const handleCloseModal = () => {
  showConsumptionModal.value = false
}

// 切换标签页
const handleTabChange = (tab) => {
  if (activeTab.value === tab) return

  // 设置标签页切换状态，防止触发上拉加载
  isTabChanging.value = true

  activeTab.value = tab

  // 重置分页数据
  currentPage.value = 1
  hasMoreData.value = true

  // 先设置loading状态，然后清理数据并加载新数据
  if (tab === 'consumption') {
    loading.value = true
    consumptionList.value = []
    // 获取消耗明细
    fetchPowerLogs(1, false, 'consumption').finally(() => {
      // 延迟重置状态，避免立即触发上拉加载
      setTimeout(() => {
        isTabChanging.value = false
      }, 500)
    })
  } else if (tab === 'allocation') {
    loading.value = true
    allocationList.value = []
    // 获取分配明细
    fetchPowerLogs(1, false, 'allocation').finally(() => {
      // 延迟重置状态，避免立即触发上拉加载
      setTimeout(() => {
        isTabChanging.value = false
      }, 500)
    })
  }
}

// 团队可用算力点击
const handleTeamAvailablePower = () => {
  uni.showToast({
    title: '可用算力详情',
    icon: 'none'
  })
}

// 获取团队算力信息
const fetchTeamComputePower = async () => {
  try {
    const cid = getCid()
    const userId = getUserId()

    if (!cid || !userId) {
      uni.showToast({
        title: '缺少必要参数',
        icon: 'none'
      })
      return
    }

    const response = await teamService.getTeamComputePower({
      cid: parseInt(cid),
      user_id: parseInt(userId)
    })

    console.log('团队算力信息:', response)

    if (response && response.status_code === 1) {
      teamComputePowerData.value = response.data

      // 更新界面显示的算力数据
      // 可用算力
      teamAvailablePower.value = response.data.available_power || 0

      // 团队剩余算力 = 可用算力（因为API返回的available_power就是可用的）
      teamRemainingPower.value = response.data.available_power || 0

      // 已使用算力
      totalConsumption.value = response.data.used_power || 0

      console.log('算力数据更新:', {
        available_power: response.data.available_power,
        used_power: response.data.used_power,
        total_power: response.data.total_power
      })
    } else {
      uni.showToast({
        title: response.message || '获取算力信息失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('获取团队算力信息失败:', error)
    uni.showToast({
      title: error?.data?.message || '获取算力信息失败',
      icon: 'none'
    })
  }
}

// 获取算力日志
const fetchPowerLogs = async (page = 1, isLoadMore = false, queryType = null) => {
  try {
    loading.value = true

    const params = {
      page: page,
      size: pageSize.value
    }

    // 根据当前标签页设置查询类型
    if (queryType) {
      params.query_type = queryType
    } else {
      // 如果没有指定queryType，根据当前活跃标签页设置
      if (activeTab.value === 'consumption') {
        params.query_type = 'consumption'
      } else if (activeTab.value === 'allocation') {
        params.query_type = 'allocation'
      }
    }

    const response = await teamService.getComputePowerLogs(params)

    console.log('算力日志:', response)

    if (response && response.status_code === 1) {
      // 接口直接返回数组
      const logs = response.data || []

      // 处理数据
      const processedLogs = logs.map(log => ({
        id: log.id,
        title: getLogTypeText(log.log_type, log.remark),
        time: formatTime(log.create_time),
        power: Math.abs(log.amount || 0),
        status: log.status,
        businessId: log.business_id,
        logType: log.log_type,
        remark: log.remark
      }))

      // 根据当前活跃标签页更新对应的列表
      if (activeTab.value === 'consumption') {
        if (isLoadMore) {
          consumptionList.value = [...consumptionList.value, ...processedLogs]
        } else {
          consumptionList.value = processedLogs
        }
      } else if (activeTab.value === 'allocation') {
        if (isLoadMore) {
          allocationList.value = [...allocationList.value, ...processedLogs]
        } else {
          allocationList.value = processedLogs
        }
      }

      // 检查是否还有更多数据（如果返回的数据少于请求的size，说明没有更多数据了）
      hasMoreData.value = logs.length === pageSize.value
      currentPage.value = page
    } else {
      uni.showToast({
        title: response.message || '获取算力日志失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('获取算力日志失败:', error)
    uni.showToast({
      title: error?.data?.message || '获取算力日志失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 格式化时间显示
const formatTime = (timeStr) => {
  if (!timeStr) return ''

  try {
    const date = new Date(timeStr)
    const now = new Date()
    const diff = now - date

    // 小于1分钟显示"刚刚"
    if (diff < 60000) {
      return '刚刚'
    }

    // 小于1小时显示"X分钟前"
    if (diff < 3600000) {
      return `${Math.floor(diff / 60000)}分钟前`
    }

    // 小于24小时显示"X小时前"
    if (diff < 86400000) {
      return `${Math.floor(diff / 3600000)}小时前`
    }

    // 超过24小时显示具体日期
    const month = date.getMonth() + 1
    const day = date.getDate()
    const hour = date.getHours().toString().padStart(2, '0')
    const minute = date.getMinutes().toString().padStart(2, '0')

    return `${month}月${day}日 ${hour}:${minute}`
  } catch (error) {
    console.error('时间格式化失败:', error)
    return timeStr
  }
}

// 获取操作类型的显示文本
const getLogTypeText = (logType, remark) => {
  const typeMap = {
    'compute_allocate': '算力分配',
    'video_generate': '视频生成',
    'image_generate': '图片生成',
    'audio_generate': '音频生成',
    'text_generate': '文案生成',
    'compute_consume': '算力消耗',
    'compute_refund': '算力退还'
  }

  return typeMap[logType] || remark || '算力操作'
}



// 初始化数据
const initData = async () => {
  await Promise.all([
    fetchTeamComputePower(),
    fetchPowerLogs(1, false, 'consumption') // 默认加载消耗明细
  ])
}



// 上拉加载更多
const handleLoadMore = () => {
  // 如果正在切换标签页，不触发上拉加载
  if (isTabChanging.value) {
    return
  }

  if (hasMoreData.value && !loading.value) {
    // 根据当前标签页传递正确的query_type
    const queryType = activeTab.value === 'consumption' ? 'consumption' : 'allocation'
    fetchPowerLogs(currentPage.value + 1, true, queryType)
  }
}

// 页面加载时初始化
onMounted(() => {
  initData()
})
</script>

<style lang="scss" scoped>
.power-details-page {
  min-height: 100vh;
  background: linear-gradient(180deg, #ffdede 0%, #f5f5f5 30%, #f9f9f9 60%, #ffffff 100%);
  position: relative;
  display: flex;
  flex-direction: column;
}

// 顶部导航栏
.power-details-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  height: calc(88rpx + var(--status-bar-height));
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: none;
}

.power-details-navbar {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0 24rpx;
  padding-top: var(--status-bar-height);
  box-sizing: border-box;
}

.power-details-navbar-left {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.power-details-navbar-back {
  width: 40rpx;
  height: 40rpx;
}

.power-details-navbar-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #000;
  flex: 1;
  text-align: center;
}

.power-details-navbar-right {
  width: 120rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.power-details-navbar-consumption {
  font-size: 28rpx;
  color: #666;
  font-weight: 400;
}

// 固定内容区域
.power-details-fixed-content {
  margin-top: calc(88rpx + var(--status-bar-height));
  padding: 24rpx 24rpx 0 24rpx;
  background: transparent;
}

// 滚动内容区域
.power-details-scroll-content {
  height: calc(100vh - 88rpx - var(--status-bar-height) - 280rpx);
  padding: 0 24rpx 24rpx 24rpx;
  overflow: hidden;
}

// 算力概览卡片
.power-details-overview {
  display: flex;
  flex-direction: row;
  background: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  padding: 32rpx 24rpx;
  margin-bottom: 32rpx;
}

.power-details-overview-left {
  display: flex;
  flex-direction: column;
  flex: 1;
  align-items: center;
}

.power-details-overview-right {
  display: flex;
  flex-direction: column;
  flex: 1;
  align-items: center;
}

.power-details-overview-value {
  font-size: 48rpx;
  font-weight: 500;
  color: #000;
  margin-bottom: 8rpx;
}

.power-details-overview-label {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.power-details-overview-text {
  font-size: 26rpx;
  color: #666;
  margin-right: 8rpx;
}

.power-details-overview-arrow {
  width: 20rpx;
  height: 20rpx;
}

// 标签页导航
.power-details-tabs {
  display: flex;
  flex-direction: row;
  margin-bottom: 24rpx;
}

.power-details-tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  position: relative;
  padding: 16rpx 0;
  cursor: pointer;
}

.power-details-tab-text {
  font-size: 28rpx;
  transition: all 0.3s ease;
}

// 下划线指示器
.power-details-tab-indicator {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #4CAF50;
  border-radius: 2rpx;
}

// 内容展示区域
.power-details-content-area {
  background: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  min-height: 300rpx;
  width: 100%;
}

// 加载状态
.power-details-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  background: #fff;
  margin: 24rpx 0;
  border-radius: 12rpx;
}

.power-details-loading-text {
  font-size: 28rpx;
  color: #666;
}

.power-details-consumption {
  width: 100%;
}

.power-details-allocation {
  width: 100%;
}

// 空状态
.power-details-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
}

.power-details-empty-icon {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 32rpx;
}

.power-details-empty-folder {
  position: absolute;
  width: 80rpx;
  height: 60rpx;
  background-color: #f0f0f0;
  border-radius: 8rpx;
}

.power-details-empty-folder-back {
  top: 20rpx;
  left: 20rpx;
  transform: rotate(-5deg);
}

.power-details-empty-folder-front {
  top: 10rpx;
  left: 30rpx;
  transform: rotate(5deg);
}

.power-details-empty-sparkle {
  position: absolute;
  width: 8rpx;
  height: 8rpx;
  background-color: #f0f0f0;
  border-radius: 50%;
}

.power-details-empty-sparkle-1 {
  top: 0;
  right: 20rpx;
}

.power-details-empty-sparkle-2 {
  top: 40rpx;
  right: 0;
}

.power-details-empty-sparkle-3 {
  bottom: 20rpx;
  right: 40rpx;
}

.power-details-empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.power-details-empty-emoji {
  font-size: 32rpx;
}

// 列表样式
.power-details-list {
  width: 100%;
}

.power-details-list-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.power-details-list-item:last-child {
  border-bottom: none;
}

.power-details-list-left {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex: 1;
  min-width: 0;
}



.power-details-list-info {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 0;
}

.power-details-list-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  min-width: 80rpx;
}

.power-details-list-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  word-break: break-all;
  margin-bottom: 8rpx;
}

.power-details-list-time {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 4rpx;
}

.power-details-list-remark {
  font-size: 24rpx;
  color: #666;
  margin-top: 8rpx;
  line-height: 1.4;
}

.power-details-list-remark-rollback {
  color: #ff4d4f;
}

.power-details-list-status {
  font-size: 22rpx;
  padding: 2rpx 8rpx;
  border-radius: 4rpx;
  text-align: center;
}

.power-details-list-status-success {
  color: #52c41a;
  background-color: #f6ffed;
}

.power-details-list-status-failed {
  color: #ff4d4f;
  background-color: #fff2f0;
}

.power-details-list-status-pending {
  color: #faad14;
  background-color: #fffbe6;
}

.power-details-list-status-rollback {
  color: #8c8c8c;
  background-color: #f5f5f5;
}

.power-details-list-power {
  font-size: 28rpx;
  font-weight: 500;
  text-align: right;
  margin-bottom: 4rpx;
}

.power-details-list-power-consumption {
  color: #ff4d4f;
}

.power-details-list-power-allocation {
  color: #52c41a;
}

// 加载更多提示
.power-details-load-more {
  padding: 32rpx 24rpx 120rpx 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.power-details-load-more-text {
  font-size: 28rpx;
  color: #BFBFBF;
  line-height: 40rpx;
  text-align: center;
}

// 弹窗样式
.power-details-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 200;
  display: flex;
  align-items: center;
  justify-content: center;
}

.power-details-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.power-details-modal-content {
  position: relative;
  width: 600rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 48rpx;
  margin: 0 48rpx;
  max-height: 80vh;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.power-details-modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
  text-align: center;
}

.power-details-modal-content-text {
  font-size: 28rpx;
  color: #000;
  margin-bottom: 48rpx;
  text-align: center;
}

.power-details-modal-button {
  width: 100%;
  height: 88rpx;
  background-color: #4CAF50;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.power-details-modal-button-text {
  font-size: 32rpx;
  color: #fff;
  font-weight: 600;
}
</style>