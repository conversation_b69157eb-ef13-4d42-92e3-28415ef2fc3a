<template>
  <transition name="slide-up-dialog">
    <view class="video-doc-dialog-mask" @click.self="onClose">
      <view class="video-doc-dialog-container">
        <!-- 全局loading遮罩 -->
        <!-- <view v-if="props.videoDocLoadingIdx !== null" class="video-doc-dialog-global-loading">
          <view class="video-doc-dialog-loading-spinner"></view>
          <text class="video-doc-dialog-loading-text">文案生成中...</text>
        </view> -->
        <!-- Header -->
        <view class="video-doc-dialog-header">
          <text class="video-doc-dialog-title">视频文案设置</text>
          <text class="video-doc-dialog-close" @click="onClose">×</text>
        </view>
        <!-- 文案列表 -->
        <scroll-view scroll-y class="video-doc-dialog-list" ref="docListScroll" :scroll-into-view="scrollToId">
          <view v-for="(doc, idx) in docs" :key="idx" :id="'doc-item-' + idx" class="video-doc-item">
            <view class="video-doc-item-title-row">
              <image class="video-doc-item-icon" src="@/asset/img/create/video_doc_icon.png" />
              <text class="video-doc-item-title">第{{ idx + 1 }}条文案</text>
            </view>
            <view class="video-doc-item-content-wrap">
              <textarea class="video-doc-item-content" v-model="docs[idx].content" :placeholder="'请输入文案内容'" auto-height maxlength="200" rows="3" @input="onInput(idx, $event)" />
              <view class="video-doc-item-actions">
                <view class="delete" @click="onDelete(idx)">
                  <image class="video-doc-btn-delete-icon" src="@/asset/img/create/delete.png" />
                </view>
                <view class="video-doc-btn-group">
                  <button class="video-doc-btn" @click="onRegenerate(idx)">
                    <image class="video-doc-btn-icon" src="@/asset/img/create/refresh_icon.png" />
                    重新生成
                  </button>
                  <button class="video-doc-btn" @click="onCopyAdd(idx)" :disabled="!docs[idx].content"><image class="video-doc-btn-icon" src="@/asset/img/create/copy_icon.png" />仿写增加一条</button>
                </view>
              </view>
            </view>
          </view>
        </scroll-view>
        <!-- Footer -->
        <view class="video-doc-dialog-footer">
          <button class="video-doc-footer-btn add" @click="showDocDialog = true">增加文案</button>
          <button class="video-doc-footer-btn confirm" @click="onConfirm">确定</button>
        </view>
      </view>
    </view>
  </transition>
  <component :is="DocDialog.default" v-if="showDocDialog" @close="showDocDialog = false" @confirm="onDocDialogConfirm" />
</template>

<script setup lang="ts">
import { ref, watch, defineProps, defineEmits, nextTick } from 'vue'
import * as DocDialog from './DocDialog.vue'
const props = defineProps({
  visible: Boolean,
  modelValue: {
    type: Array as () => { content: string }[],
    default: () => [{ content: '' }]
  },
  videoDocLoadingIdx: Number // 新增，当前生成中的索引
})
const emit = defineEmits(['close', 'confirm', 'add', 'regenerate', 'copyAdd', 'delete', 'update:modelValue'])
const docs = ref(props.modelValue.length ? props.modelValue.slice() : [{ content: '' }])
watch(
  () => props.modelValue,
  val => {
    docs.value = val.length ? val.slice() : [{ content: '' }]
  }
)
const showDocDialog = ref(false)
const pendingDocContent = ref('')
const scrollToId = ref('')
const docListScroll = ref<any>(null)
function onClose() {
  emit('close')
}
function onConfirm() {
  emit('confirm', docs.value)
}
function onAdd(content = '') {
  docs.value.push({ content })
  emit('add', docs.value.length - 1)
  emit('update:modelValue', docs.value)
  nextTick(() => {
    const scrollView = docListScroll.value
    if (scrollView && scrollView.$el) {
      // H5
      scrollView.$el.scrollTop = scrollView.$el.scrollHeight
    } else if (scrollView && scrollView.scrollTop !== undefined) {
      // 小程序
      scrollView.scrollTop = 99999
    }
  })
}
function onRegenerate(idx: number) {
  emit('regenerate', idx)
}
function onCopyAdd(idx: number) {
  emit('copyAdd', idx)
}
function onDelete(idx: number) {
  docs.value.splice(idx, 1)
  emit('delete', idx)
  emit('update:modelValue', docs.value)
}
function onInput(idx: number, e: any) {
  let value = ''
  if (e && typeof e === 'object') {
    if (e.detail && typeof e.detail.value === 'string') {
      value = e.detail.value
    } else if (e.target && typeof e.target.value === 'string') {
      value = e.target.value
    }
  }
  docs.value[idx].content = value
  emit('update:modelValue', docs.value)
}
function onDocDialogConfirm(payload) {
  showDocDialog.value = false
  // 这里可以将payload.docType和payload.customRequest用于文案生成
  emit('add', payload)
}
</script>

<style scoped>
.slide-up-dialog-enter-active,
.slide-up-dialog-leave-active {
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}
.slide-up-dialog-enter-from,
.slide-up-dialog-leave-to {
  opacity: 0;
  transform: translateY(100%);
}
.slide-up-dialog-enter-to,
.slide-up-dialog-leave-from {
  opacity: 1;
  transform: translateY(0);
}
.video-doc-dialog-mask {
  position: fixed;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  z-index: 900;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}
.video-doc-dialog-container {
  background: #1d1d1e;
  border-radius: 32rpx 32rpx 0 0;
  width: 100vw;
  max-width: 750rpx;
  box-sizing: border-box;
  padding: 0 0 32rpx 0;
  box-shadow: 0 -8rpx 32rpx 0 rgba(0, 0, 0, 0.18);
  min-height: 60vh;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}
.video-doc-dialog-header {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  height: 100rpx;
  border-bottom: 2rpx solid #353537;
  background: #1d1d1e;
  border-radius: 32rpx 32rpx 0 0;
  position: relative;
}
.video-doc-dialog-title {
  flex: 1;
  text-align: center;
  color: #fff;
  font-size: 34rpx;
  font-weight: 500;
}
.video-doc-dialog-close {
  position: absolute;
  right: 32rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 44rpx;
  color: #bfbfbf;
  line-height: 1;
  cursor: pointer;
}
.video-doc-dialog-list {
  flex: 1;
  overflow-y: auto;
  padding: 0 24rpx;
}
.video-doc-item {
  margin-top: 20rpx;
  padding: 20rpx 16rpx 16rpx 16rpx;
  box-sizing: border-box;
  box-shadow: 0 2rpx 8rpx 0 rgba(0, 0, 0, 0.1);
}
.video-doc-item-title-row {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}
.video-doc-item-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
}
.video-doc-item-title {
  color: #fff;
  font-size: 30rpx;
  font-weight: 700;
}
.video-doc-item-content-wrap {
  background: #232325;
  border-radius: 14rpx;
  padding: 18rpx 16rpx 24rpx 16rpx;
  margin-bottom: 0;
}
.video-doc-item-content {
  width: 100%;
  background: transparent;
  border: none;
  color: #bfbfbf;
  min-height: 120rpx;
  resize: none;
  outline: none;
  font-size: 24rpx;
  line-height: 36rpx;
}
.video-doc-item-actions {
  margin-top: 10rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 0;
}
.video-doc-btn {
  background-color: #383839;
  color: #bfbfbf;
  font-size: 24rpx;
  border-radius: 10rpx;
  height: 52rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1rpx solid #353537;
  outline: none;
  cursor: pointer;
  font-weight: 500;
  padding: 0 20rpx;
  transition: background 0.2s, color 0.2s;
}
.video-doc-btn:disabled {
  opacity: 0.7;
  background-color: #383839 !important;
  color: #bfbfbf !important;
}
.video-doc-btn-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 10rpx;
}
.video-doc-item-actions .delete {
  background: transparent;
  border: none;
  padding: 0;
  margin-right: 12rpx;
  min-width: 0;
  width: auto;
  height: auto;
  display: flex;
  align-items: center;
  justify-content: center;
}
.video-doc-btn-delete-icon {
  width: 36rpx;
  height: 36rpx;
  display: block;
}
.video-doc-btn-group {
  display: flex;
  gap: 12rpx;
}
.video-doc-dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 32rpx;
  padding: 0 32rpx;
  gap: 24rpx;
}
.video-doc-footer-btn {
  flex: 1;
  font-size: 30rpx;
  border-radius: 16rpx;
  height: 104rpx;
  line-height: 104rpx;
  padding: 0;
  border: none;
  outline: none;
  margin: 0 8rpx;
  font-weight: 500;
  transition: background 0.2s, color 0.2s;
}
.video-doc-footer-btn.add {
  background: #353537;
  color: #fff;
}
.video-doc-footer-btn.confirm {
  background: #ff0043;
  color: #fff;
}
.video-doc-dialog-global-loading {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.45);
  z-index: 10000;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.video-doc-dialog-loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #fff;
  border-top: 6rpx solid #ff0043;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 18rpx;
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.video-doc-dialog-loading-text {
  color: #fff;
  font-size: 28rpx;
  font-weight: 500;
}
button.video-doc-btn[disabled] {
  background-color: #383839 !important;
  opacity: 0.7;
  color: #bfbfbf !important;
}
</style>
