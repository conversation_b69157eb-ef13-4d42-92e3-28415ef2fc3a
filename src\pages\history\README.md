# 创作历史页面

## 组件结构

创作历史页面由以下组件组成：

1. **index.vue** - 主页面组件，负责整体布局和数据管理
2. **HistoryFilterBar.vue** - 顶部筛选栏组件，提供日期和状态筛选功能
3. **HistoryCard.vue** - 历史记录卡片组件，展示单个创作项目的信息和状态

## HistoryFilterBar 组件

顶部筛选栏组件，提供日期范围选择和状态筛选功能。组件内部管理自己的状态，简化了父组件的使用方式。

### 事件

| 事件名 | 参数 | 说明 |
|-------|------|------|
| filter | {dateRange, status} | 筛选条件变更时触发，包含当前的筛选值 |

### 暴露方法

| 方法名 | 返回值 | 说明 |
|-------|------|------|
| getFilters | {dateRange, status} | 获取当前的筛选条件 |

## HistoryCard 组件

历史记录卡片组件，展示单个创作项目的信息和状态。组件接收完整的数据项对象，简化了属性传递。包含完整的卡片容器、日期显示、主卡片区和卡片区四部分内容，是一个完全自包含的组件。

### 属性

| 属性名 | 类型 | 默认值 | 说明 |
|-------|------|-------|------|
| item | Object | 必填 | 完整的创作项目数据对象 |

### 事件

| 事件名 | 参数 | 说明 |
|-------|------|------|
| action | {type, item} | 统一的事件处理，type 表示操作类型，item 是当前操作的数据项 |

### 操作类型

- `play`: 播放视频
- `delete`: 删除创作
- `download`: 下载视频
- `publish`: 发布创作
- `retry`: 重试失败的创作

### 数据结构

主卡片区使用 `item` 对象的属性：
```javascript
{
  id: 1,
  title: "视频标题",
  create_time: "2023-01-01 12:00:00",
  user_script: "视频描述文本...",
  cover_image_path_auth: "封面图片URL",
  output_path_auth: "视频URL",
  status: 4, // 状态码
  vod_video_id: "视频ID"
}
```

卡片区使用 `item.items` 数组，每个子项的结构：
```javascript
{
  img: "图片URL",
  status: "generating", // 'generating' | 'completed' | 'wait'
  estimatedTime: "预计还需10分钟", // 仅生成中状态有
  count: 4, // 仅等待状态有
  duration: "00:12" // 仅视频有
}
```

## 进度条状态配置

组件内置了不同状态的进度条配置：

```javascript
const progressConfig = {
  0: { text: '待处理... 0%', percent: 0, width: '0%', color: '#F5F5F5', textColor: '#8C8C8C' },
  1: { text: '剪辑中... 25%', percent: 25, width: '25%', color: '#E3F2FD', textColor: '#1976D2' },
  2: { text: '剪辑中... 50%', percent: 50, width: '50%', color: '#BBDEFB', textColor: '#1565C0' },
  3: { text: '剪辑中... 75%', percent: 75, width: '75%', color: '#90CAF9', textColor: '#0D47A1' },
  4: { text: '已完成 100%', percent: 100, width: '100%', color: '#C8E6C9', textColor: '#2E7D32' },
  5: { text: '生成失败 0%', percent: 0, width: '0%', color: '#FFCDD2', textColor: '#C62828' }
}
```

## 使用示例

```vue
<template>
  <!-- 筛选栏 -->
  <HistoryFilterBar
    @filter="handleFilterChange"
    ref="filterBar"
  />
  
  <!-- 历史记录列表 -->
  <view v-for="item in historyList" :key="item.id" class="history-group">
    <text class="history-group__date">{{ formatTime(item.create_time, 'YYYY-MM-DD') }}</text>
    <HistoryCard
      :item="item"
      @action="handleCardAction"
    />
  </view>
</template>

<script setup>
// 处理筛选条件变更
function handleFilterChange(filters) {
  // 更新筛选条件并加载数据
  dateFilter.value = filters.dateRange
  statusFilter.value = filters.status
  fetchCreateHistroyData()
}

// 处理卡片操作
function handleCardAction({ type, item }) {
  switch (type) {
    case 'play':
      handlePlayVideo(item)
      break
    case 'delete':
      deleteHistory(item.id)
      break
    case 'download':
      handleDownload(item)
      break
    case 'publish':
      handleMaincardAction('publish', item)
      break
    case 'retry':
      handleMaincardAction('retry', item)
      break
  }
}
</script>
```