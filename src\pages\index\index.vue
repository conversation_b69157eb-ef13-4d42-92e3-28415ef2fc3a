<template>
  <view class="home flex-col">
    <!-- 固定头部 -->
    <view class="home-header flex-col">
      <view class="status-bar"></view>
      <image class="home-header-bg" src="@/asset/img/index/header_bg.png" mode="aspectFill" />
      <view class="home-header__section flex-col">
        <view class="home-header__logo-row flex-row justify-between">
          <image class="home-header__logo" src="@/asset/img/index/header_logo.png" />
          <!-- <image class="home-header__settings" src="@/asset/img/index/header_settings.png" /> -->
        </view>
        <view class="home-header__main">
          <view class="home-header__main-btn flex-row" @click="goToCreate">
            <view class="home-header__main-btn-content flex-col">
              <image class="home-header__main-btn-icon" src="@/asset/img/index/main_btn_icon.png" />
              <text class="home-header__main-btn-text">开始创作</text>
            </view>
          </view>
        </view>

      </view>
    </view>
    <!-- 内容区可滚动 -->
    <scroll-view class="home-main-scroll" scroll-y="true">
      <view class="home-main flex-col">
        <!-- 功能卡片（原悬浮卡片，横向排列） -->
        <view class="home-main__feature-cards flex-row" @click="handleFeatureCard()">
          <view class="home-main__feature-card flex-col">
            <image class="home-main__feature-card-icon" src="@/asset/img/index/feature_card_ai_video.png"  mode="aspectFit" />
            <text class="home-main__feature-card-title">AI穿版视频</text>
            <text class="home-main__feature-card-sub">一键生成视频</text>
          </view>
          <view class="home-main__feature-card flex-col">
            <image class="home-main__feature-card-icon" src="@/asset/img/index/feature_card_ai_copywriting.png"  mode="aspectFit" />
            <text class="home-main__feature-card-title">AI爆款文案</text>
            <text class="home-main__feature-card-sub">AI字幕动效包装</text>
          </view>
          <view class="home-main__feature-card flex-col">
            <image class="home-main__feature-card-icon" src="@/asset/img/index/feature_card_template.png" mode="aspectFit" />
            <text class="home-main__feature-card-title">模版视频</text>
            <text class="home-main__feature-card-sub">30+数字模版</text>
          </view>
        </view>
        <text class="home-main__title">更多功能</text>
        <view class="home-main__features flex-row" @click="handleFeatureCard()">
          <view class="home-feature__card flex-col" v-for="(item, index) in featureList" :key="index">
            <view class="home-feature__icon-wrap flex-col">
              <image class="home-feature__icon" :src="$tools.processVodImageUrl(item.icon)" mode="aspectFit" />
            </view>
            <text class="home-feature__text">{{ item.text }}</text>
          </view>
        </view>
        <!-- <image class="home-main__divider" src="@/asset/img/index/main_divider.png" /> -->
        <text class="home-main__subtitle">新手教程</text>
        <view class="home-main__tutorials flex-row justify-between">
          <view class="home-tutorial__card flex-col home-tutorial__card--ai-wear" @click="handleTutorial()">
            <image class="home-tutorial__icon" src="@/asset/img/index/tutorial_ai_wear.png" />
            <view class="home-tutorial__text-group">
              <text class="home-tutorial__text-main">AI穿版</text>
              <text class="home-tutorial__text-sub">照片变成上身效果</text>
            </view>
          </view>
          <view class="home-tutorial__card flex-col home-tutorial__card--light" @click="handleTutorial()">
            <image class="home-tutorial__icon" src="@/asset/img/index/tutorial_ai_wear.png" />
            <view class="home-tutorial__text-group">
              <text class="home-tutorial__text-main">调光成片</text>
              <text class="home-tutorial__text-sub">光随心意调，成片即出挑</text>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>
<script setup>
import { onShow } from '@dcloudio/uni-app'
import featureOneClickVideo from '@/asset/img/index/feature_one_click_video.png'
import featureCopyExtract from '@/asset/img/index/feature_copy_extract.png'
import featureLightVideo from '@/asset/img/index/feature_light_video.png'
import featureAccountManage from '@/asset/img/index/feature_account_manage.png'
import featureTaskDistribute from '@/asset/img/index/feature_task_distribute.png'
import featurePrivateMsg from '@/asset/img/index/feature_private_msg.png'
import featureCommentManage from '@/asset/img/index/feature_comment_manage.png'
import featureDataStat from '@/asset/img/index/feature_data_stat.png'

const featureList = [
  {
    icon: featureOneClickVideo,
    text: '一键成片',
  },
  {
    icon: featureCopyExtract,
    text: '文案提取',
  },
  {
    icon: featureLightVideo,
    text: '调光成片',
  },
  {
    icon: featureAccountManage,
    text: '账号管理',
  },
  {
    icon: featureTaskDistribute,
    text: '分发任务',
  },
  {
    icon: featurePrivateMsg,
    text: '私信管理',
  },
  {
    icon: featureCommentManage,
    text: '评论管理',
  },
  {
    icon: featureDataStat,
    text: '数据统计',
  },
]
function goToCreate() {
  uni.navigateTo({ url: '/pages/create/index' })
}

function handleFeatureCard() {
  uni.showToast({
    title: '功能正在开发中',
    icon: 'none',
  })
}

function handleTutorial() {
  uni.showToast({
    title: '教程正在制作中',
    icon: 'none',
  })
}

onShow(() => {
  uni.setTabBarStyle({
    "color": "rgba(255, 255, 255, 0.7)",
    "selectedColor": "#ff2d55",
    "backgroundColor": "#141414",
    "borderStyle": "black",
  })
})
</script>
<style scoped>
.status-bar {
  height: var(--status-bar-height);
  width: 100%;
}

.home {
  background: #000;
  height: 100vh;
  width: 100vw;
  position: relative;
  overflow-x: hidden;
  overflow-y: hidden;
}

.home-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  width: 100vw;
  padding: 0;
}
.home-header-bg {
  position: absolute;
  left: 50%;
  top: 0;
  height: 308rpx;
  transform: translateX(-50%);
  width: auto;
  min-width: 100vw;
  max-width: none;
  object-fit: contain;
  z-index: 1;
  pointer-events: none;
}
.home-header__statusbar {
  display: flex;
  align-items: center;
  padding: 0 40rpx;
  height: 48rpx;
  margin-top: 20rpx;
}
.home-header__time {
  color: #fff;
  font-size: 30rpx;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  margin-right: auto;
}
.home-header__icon {
  height: 22rpx;
  margin-left: 10rpx;
}
.home-header__logo-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 0 24rpx;
  margin: 36rpx auto 0 auto;
}
.home-header__logo {
  width: 180rpx;
  height: 56rpx;
}
.home-header__settings {
  width: 36rpx;
  height: 34rpx;
}
.home-header__main {
  padding: 0 40rpx;
  position: relative;
}
.home-header__main-btn {
  background: url(@/asset/img/index/main_btn_bg.png) no-repeat;
  background-size: 100% 200rpx;
  margin: 36rpx auto 0 auto;
  padding: 42rpx 0 28rpx 0;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  border-radius: 24rpx;
}
.home-header__main-btn-icon {
  width: 84rpx;
  height: 84rpx;
  margin: auto;
}
.home-header__main-btn-text {
  color: #fff;
  font-size: 32rpx;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  margin-top: 2rpx;
  line-height: 44rpx;
}

.home-divider {
  width: 100vw;
  height: 68rpx;
  margin-top: 460rpx;
}

.home-main-scroll {
  position: absolute;
  top: calc(360rpx + var(--status-bar-height));
  left: 0;
  right: 0;
  bottom: 0;
  overflow-y: auto;
  background: transparent;
  height: calc(100vh - var(--status-bar-height) - 362rpx);
}
.home-main {
  width: 100%;
  margin: 0 auto;
  padding: 0 24rpx;
}
.home-main__title {
  color: #fff;
  font-size: 30rpx;
  font-weight: normal;
}
.home-main__features {
  display: flex;
  flex-wrap: wrap;
  margin: 20rpx 20rpx 0 0;
  gap: 24rpx 0;
}
.home-feature__card {
  width: 25%;
  height: 120rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.home-feature__icon-wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48rpx;
  height: 48rpx;
}
.home-feature__icon {
  width: 40rpx;
  height: 40rpx;
  object-fit: contain;
}
.home-feature__text {
  color: #fff;
  font-size: 24rpx;
  margin-top: 8rpx;
  text-align: center;
}

.home-main__divider {
  width: 88rpx;
  height: 28rpx;
  display: block;
  margin: 34rpx auto 0 auto;
}
.home-main__subtitle {
  color: #fff;
  font-size: 30rpx;
  margin: 28rpx 0 0 24rpx;
  font-weight: normal;
}
.home-main__tutorials {
  width: 100%;
  display: flex;
  justify-content: space-between;
  margin: 28rpx auto 0 auto;
  gap: 20rpx;
  margin-bottom: 30rpx;
}
.home-tutorial__card {
  border-radius: 32rpx;
  padding: 94rpx 140rpx 16rpx 28rpx;
  display: flex;
  flex-direction: column;
  width: 351rpx;
  flex: 1;
  box-sizing: border-box;
  position: relative;
}

.home-tutorial__card--ai-wear {
  background-image: url('@/asset/img/index/tutorial_ai_wear_bg.png');
  background-size: cover;
  background-position: center;
}

.home-tutorial__card--light {
  background-image: url('@/asset/img/index/tutorial_light_bg.png');
  background-size: cover;
  background-position: center;
  padding-right: 74rpx;
}
.home-tutorial__icon {
  width: 52rpx;
  height: 52rpx;
  margin: 0 6rpx 0 118rpx;
}

.home-tutorial__card--light .home-tutorial__icon {
  margin: 0 72rpx 0 118rpx;
}
.home-tutorial__text-group {
  margin-top: 30rpx;
}

.home-tutorial__text-main {
  color: rgba(255, 255, 255, 1);
  font-size: 26rpx;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 36rpx;
  margin-right: 98rpx;
}

.home-tutorial__card--light .home-tutorial__text-main {
  margin-right: 138rpx;
}

.home-tutorial__text-sub {
  color: rgba(255, 255, 255, 1);
  font-size: 22rpx;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
  margin-top: 2rpx;
}

.home-main__tabbar {
  background: #141414;
  margin-top: 86rpx;
  padding: 18rpx 46rpx 18rpx 52rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.home-tabbar__item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}
.home-tabbar__icon {
  width: 44rpx;
  height: 44rpx;
}
.home-tabbar__text--active {
  color: #ff0043;
  font-size: 22rpx;
  margin-top: 4rpx;
}
.home-tabbar__text {
  color: #8c8c8c;
  font-size: 22rpx;
  margin-top: 4rpx;
}

.home-main__floating {
  position: absolute;
  right: 48rpx;
  top: -34rpx;
  width: 220rpx;
  height: 192rpx;
  background: #18181b;
  border-radius: 30rpx;
  border: 2rpx solid #232325;
  padding: 32rpx 44rpx 20rpx 44rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.home-main__floating-icon {
  width: 48rpx;
  height: 48rpx;
  margin: 0 42rpx 0 38rpx;
}
.home-main__floating-title {
  color: #fff;
  font-size: 26rpx;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  margin: 18rpx 0 0 0;
}
.home-main__floating-sub {
  color: #fff;
  font-size: 22rpx;
  margin-top: 2rpx;
}

.home-main__floating2 {
  position: absolute;
  left: 262rpx;
  top: -34rpx;
  width: 220rpx;
  height: 192rpx;
  background: #18181b;
  border-radius: 30rpx;
  border: 2rpx solid #232325;
  padding: 30rpx 42rpx 20rpx 42rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.home-main__floating2-icon {
  width: 62rpx;
  height: 52rpx;
  margin: 0 34rpx 0 36rpx;
}
.home-main__floating2-title {
  color: #fff;
  font-size: 26rpx;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  margin: 16rpx 0 0 0;
}
.home-main__floating2-sub {
  color: #fff;
  font-size: 22rpx;
  margin-top: 2rpx;
}

.home-main__floating3 {
  position: absolute;
  left: 24rpx;
  top: -34rpx;
  width: 220rpx;
  height: 192rpx;
  background: #18181b;
  border-radius: 30rpx;
  border: 2rpx solid #232325;
  padding: 28rpx 30rpx 20rpx 34rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.home-main__floating3-icon {
  width: 58rpx;
  height: 56rpx;
  margin: 0 50rpx 0 44rpx;
}
.home-main__floating3-title {
  color: #fff;
  font-size: 26rpx;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  margin: 14rpx 0 0 0;
}
.home-main__floating3-sub {
  color: #fff;
  font-size: 22rpx;
  margin-top: 2rpx;
}
.home-main__feature-cards {
  display: flex;
  flex-direction: row;
  justify-content: space-evenly;
  align-items: flex-start;
  margin: 0 auto 48rpx auto;
  gap: 24rpx;
  width: 100%;
}
.home-main__feature-card {
  background: #18181b;
  border-radius: 30rpx;
  border: 2rpx solid #232325;
  padding: 30rpx 20rpx 22rpx 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 220rpx;
  min-width: 220rpx;
  height: 192rpx;
  box-sizing: border-box;
  flex: 1;
}
.home-main__feature-card-icon {
  width: 56rpx;
  height: 56rpx;
  margin: 0 0 16rpx 0;
}
.home-main__feature-card-title {
  color: #fff;
  font-size: 26rpx;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  line-height: 36rpx;
}
.home-main__feature-card-sub {
  color: #656567;
  font-size: 22rpx;
  margin-top: 2rpx;
  line-height: 32rpx;
}
.home-header__section {
  position: relative;
  z-index: 2;
}
</style>
