<template>
  <view class="profile-page">
    <!-- <view class="status-bar"></view> -->
    <!-- 固定头部区域 -->
    <view class="profile-header-wrap">
      <image class="profile-header-bg-img" src="@/asset/img/profile/profile-header-bg.png"/>
      <view class="profile-header-title">
        <text class="profile-navbar-title">我的</text>
        <!-- <view class="profile-navbar-icons">
          <image class="profile-navbar-scan" src="@/asset/img/profile/profile-navbar-scan.png" />
          <image class="profile-navbar-setting" src="@/asset/img/profile/profile-navbar-setting.png" />
        </view> -->
      </view>
      <view class="profile-header-content">
        <view class="profile-header-user">
          <image class="profile-header-avatar" src="@/asset/img/profile/profile-header-avatar.png" />
          <view class="profile-header-user-text">
            <text class="profile-header-nickname">{{ userInfo?.username }}</text>
            <text class="profile-header-id">ID: {{ userInfo?.id }}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 可滚动内容区域 -->
    <scroll-view class="profile-scroll" scroll-y="true">
      <view class="profile-content">
        <!-- 会员权益卡片 -->
        <view class="profile-vip-card" @click="todo">
          <view class="profile-vip-content">
            <text class="profile-vip-title">开通立即享受更多精彩！</text>
            <view class="profile-vip-icons">
              <view class="profile-vip-icon">
                <image class="profile-vip-icon-img" src="@/asset/img/profile/profile-vip-icon-template.png" />
                <text class="profile-vip-icon-text">专属模版</text>
              </view>
              <view class="profile-vip-icon">
                <image class="profile-vip-icon-img" src="@/asset/img/profile/profile-vip-icon-material.png" />
                <text class="profile-vip-icon-text">专属素材</text>
              </view>
              <view class="profile-vip-icon">
                <image class="profile-vip-icon-img" src="@/asset/img/profile/profile-vip-icon-rights.png" />
                <text class="profile-vip-icon-text">会员权益</text>
              </view>
              <view class="profile-vip-icon">
                <image class="profile-vip-crown" src="@/asset/img/profile/profile-vip-crown.png" />
              </view>
            </view>
          </view>
          <view class="profile-vip-btn-wrapper">
            <text class="profile-vip-btn">去开通</text>
          </view>
        </view>
        <!-- 剪辑/穿版条数卡片 -->
        <view class="profile-clip-card">
          <view class="profile-clip-item" @tap="goToTeamMember">
            <view class="profile-clip-img-wrap">
              <image class="profile-clip-under-bg" src="@/asset/img/profile/profile-clip-bg.png" />
              <view class="profile-clip-content">
                <image class="profile-clip-label" src="@/asset/img/profile/profile-clip-label.png" mode="aspectFit" />
              </view>
            </view>
            <view class="profile-clip-text">
              <text class="profile-clip-title">剪辑条数</text>
              <text class="profile-clip-count">剩余10条</text>
            </view>
          </view>
          <view class="profile-clip-item" @click="todo">
            <view class="profile-clip-img-wrap">
              <image class="profile-clip-under-bg" src="@/asset/img/profile/profile-clip-bg.png" />
              <view class="profile-clip-content">
                <image class="profile-clip-bg2" src="@/asset/img/profile/profile-clip-bg2.png" />
              </view>
            </view>
            <view class="profile-clip-text">
              <text class="profile-clip-title">穿版条数</text>
              <text class="profile-clip-count">剩余10条</text>
            </view>
          </view>
        </view>
        <!-- 功能菜单列表 -->
        <view class="profile-menu-list">
          <view
            v-for="item in menuList"
            :key="item.key"
            class="profile-menu-item"
            @click="handleMenuClick(item)"
          >
            <image class="profile-menu-icon" :src="$tools.processVodImageUrl(item.icon)" mode="aspectFit" />
            <text class="profile-menu-text">{{ item.text }}</text>
            <image class="profile-menu-arrow" src="@/asset/img/profile/profile-menu-arrow.png" />
          </view>
        </view>
        <!-- 退出登录按钮 -->
        <view class="profile-logout-btn-wrapper">
          <button class="profile-logout-btn" @click="handleLogout">退出登录</button>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import { storeToRefs } from 'pinia'
import { onShow } from '@dcloudio/uni-app'
import { useUserStore } from '@/store/modules/user'
import profileMenuClassroom from '@/asset/img/profile/profile-menu-classroom.png';
import profileMenuHelp from '@/asset/img/profile/profile-menu-help.png';
import profileMenuVip from '@/asset/img/profile/profile-menu-vip.png';
import profileMenuService from '@/asset/img/profile/profile-menu-service.png';
import profileMenuInvite from '@/asset/img/profile/profile-menu-invite.png';
import profileMenuTeam from '@/asset/img/profile/profile-menu-team.png';

const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)

// 功能菜单数据
const menuList = [
  {
    icon: profileMenuTeam,
    text: '团队成员',
    key: 'team'
  },
  {
    icon: profileMenuClassroom,
    text: '创作课堂',
    key: 'classroom'
  },
  {
    icon: profileMenuHelp,
    text: '帮助中心',
    key: 'help'
  },
  {
    icon: profileMenuVip,
    text: '我的会员',
    key: 'vip'
  },
  {
    icon: profileMenuService,
    text: '联系客服',
    key: 'service'
  },
  {
    icon: profileMenuInvite,
    text: '邀请好友',
    key: 'invite'
  }
]

function todo() {
  uni.showToast({
    title: '功能正在开发',
    icon: 'none'
  })
}

// 菜单点击处理
const handleMenuClick = (item) => {
  switch (item.key) {
    case 'team':
      uni.navigateTo({
        url: '/pages/team/index'
      })
      break
    case 'classroom':
      // 创作课堂逻辑
      todo();
      break
    case 'help':
      // 帮助中心逻辑
      todo();
      break
    case 'vip':
      // 我的会员逻辑
      todo();
      break
    case 'service':
      // 联系客服逻辑
      todo();
      break
    case 'invite':
      // 邀请好友逻辑
      todo();
      break
    default:
      break
  }
}

// 退出登录
const handleLogout = async () => {
  uni.showModal({
    title: '提示',
    content: '确定要退出登录吗？',
    success: async function (res) {
      if (res.confirm) {
        try {
          // 调用store的登出方法，清除所有状态和CID
          await userStore.logout()
          
          // 清除本地存储
          uni.clearStorageSync()
          
          // 显示退出成功提示
          uni.showToast({
            title: '已退出登录',
            icon: 'success',
            duration: 1500
          })
          
          // 延迟跳转到登录页，关闭所有页面
          setTimeout(() => {
            uni.reLaunch({
              url: '/pages/login/index'
            })
          }, 1500)
        } catch (error) {
          console.error('退出登录失败:', error)
          uni.showToast({
            title: '退出登录失败',
            icon: 'none'
          })
        }
      }
    }
  })
}

// 跳转到团队版会员页面
const goToTeamMember = () => {
  uni.navigateTo({
    url: '/pages/payment/index'
  });
};

onShow(() => {
  uni.setTabBarStyle({
    color: 'rgba(140, 140, 140, 1)',
    selectedColor: '#FF0043',
    backgroundColor: '#fff',
    borderStyle: 'white'
  })
})
</script>

<style lang="scss" scoped>
// .status-bar {
//   height: var(--status-bar-height);
//   width: 100%;
// }
// 状态栏、导航栏、通用flex
.profile-flex-row {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.profile-flex-col {
  display: flex;
  flex-direction: column;
}

.profile-page {
  position: relative;
  height: calc(100vh);
  background-color: #f9f9f9;
}

// 自定义导航栏
.profile-navbar {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  height: 56rpx;
  padding: 0 32rpx;
  background: transparent;
}
.profile-navbar-title {
  margin-left: 60rpx;
  font-size: 30rpx;
  font-weight: 500;
  color: #000;
  flex: 1;
  text-align: left;
}
.profile-navbar-icons {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 32rpx;
}
.profile-navbar-scan {
  width: 30rpx;
  height: 30rpx;
}
.profile-navbar-setting {
  width: 30rpx;
  height: 30rpx;
}

// 头部背景及用户信息
.profile-header-bg-gradient {
  width: 100%;
  height: 200rpx;
  background: linear-gradient(180deg, #ff6b81 0%, #fff 100%);
  border-radius: 0 0 32rpx 32rpx;
  box-shadow: 0 8rpx 32rpx 0 rgba(255,107,129,0.08);
  display: flex;
  align-items: flex-end;
  position: relative;
  margin-bottom: 24rpx;
}
.profile-header-wrap {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 10;
  width: 100%;
  height: 306rpx;
}
.profile-header-bg-img {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 612rpx;
  z-index: 0;
}
.profile-header-content {
  position: relative;
  z-index: 2;
  width: 100%;
  height: calc(220rpx + var(--status-bar-height));
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  padding: 0 32rpx 0 32rpx;
}
.profile-header-title {
  position: fixed;
  top: var(--status-bar-height);
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0 24rpx;
  margin-top: 36rpx;
  z-index: 9;
  overflow: hidden;
  background: transparent;
}
.profile-header-title-bg {
  position: absolute;
  left: 0;
  top: var(--status-bar-height);
  width: 100%;
  height: 628rpx;
  z-index: -1;
}
.profile-header-user {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-top: 8rpx;
}
.profile-header-avatar {
  width: 104rpx;
  height: 104rpx;
  border-radius: 50%;
  background: #fff;
  box-shadow: 0 8rpx 32rpx 0 rgba(0,0,0,0.08);
}
.profile-header-user-text {
  display: flex;
  flex-direction: column;
  margin-left: 28rpx;
}
.profile-header-nickname {
  font-size: 36rpx;
  font-weight: 500;
  color: #333;
  line-height: 48rpx;
}
.profile-header-id {
  font-size: 26rpx;
  color: #b0b0b0;
  margin-top: 10rpx;
}

.profile-vip-card {
  position: relative;
  margin: 0 0 28rpx 0;
  background: #231815;
  border-radius: 28rpx;
  min-height: 160rpx;
  box-shadow: 0 12rpx 40rpx 0 rgba(35,24,21,0.12);
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 28rpx 72rpx 32rpx 42rpx;
}
.profile-vip-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.profile-vip-title {
  font-size: 30rpx;
  color: #E7CCB3;
  font-weight: 500;
  font-size: 28rpx;
}
.profile-vip-icons {
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  justify-content: space-between;
  margin-top: 38rpx;
}
.profile-vip-icon {
  align-items: center;
  display: flex;
  flex-direction: column;
  &:last-child {
    margin-left: 40rpx;
  }
}
.profile-vip-icon-img {
  width: 40rpx;
  height: 36rpx;
}
.profile-vip-icon-text {
  font-size: 24rpx;
  color: #B9A38E;
  margin-top: 12rpx;
  font-weight: 500;
}
.profile-vip-crown {
  width: 116rpx;
  height: 104rpx;
  animation: crownDropIn 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes crownDropIn {
  0% {
    transform: translateY(-300rpx) scale(0.8);
    opacity: 0;
  }
  70% {
    transform: translateY(0) scale(1.1);
    opacity: 1;
  }
  85% {
    transform: translateY(-8rpx) scale(0.98);
    opacity: 1;
  }
  100% {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}
.profile-vip-btn-wrapper {
  background-color: rgba(255, 0, 67, 1);
  border-radius: 196rpx 16rpx 0 196rpx;
  width: 118rpx;
  position: absolute;
  top: 22rpx;
  right: -18rpx;
  padding: 4rpx 24rpx;

  &::after {
    content: '';
    width: 0;
    height: 0;
    position: absolute;
    border-left: 14rpx solid transparent;
    border-right: 14rpx solid transparent;
    border-bottom: 14rpx solid #CA0035; 
    transform: rotate(-45deg);
    bottom: -12rpx;
    right: 0;
  }
}
.profile-vip-btn {
  color: #151515;
  font-size: 24rpx;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
}
.profile-vip-bg-dot {
  width: 12rpx;
  height: 12rpx;
  position: absolute;
  right: 24rpx;
  bottom: 24rpx;
}

// 剪辑/穿版条数卡片
.profile-clip-card {
  position: relative;
  z-index: 1;
  margin: 0 0 24rpx 0;
  background: #fff;
  border-radius: 28rpx;
  box-shadow: 0 8rpx 32rpx 0 rgba(0,0,0,0.06);
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 32rpx
}
.profile-clip-item {
  position: relative;
  width: 48%;
  background: #f9f9f9;
  border-radius: 20rpx;
  padding: 28rpx 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx 0 rgba(0,0,0,0.04);
}
.profile-clip-img-wrap {
  position: relative;
  width: 74rpx;
  height: 52rpx;
  display: flex;
  align-items: flex-end;
  margin-right: 20rpx;
}
.profile-clip-under-bg {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 74rpx;
  height: 52rpx;
}
.profile-clip-content {
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
  position: relative;
  width: 52rpx;
  height: 52rpx;
  margin: 0 auto;
}
.profile-clip-label {
  width: 52rpx;
  height: 52rpx;
}
.profile-clip-bg {
  position: relative;
  width: 80rpx;
  height: 56rpx;
  margin-right: 20rpx;
}
.profile-clip-bg1 {
  width: 48rpx;
  height: 40rpx;
  position: absolute;
  left: 0;
  top: 0;
}
.profile-clip-bg2 {
  width: 46rpx;
  height: 38rpx;
  background-color: #fff;
}
.profile-clip-text {
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.profile-clip-title {
  font-size: 28rpx;
  color: #262626;
  font-weight: 600;
}
.profile-clip-count {
  font-size: 24rpx;
  color: #595959;
}

// 功能菜单列表
.profile-menu-list {
  // width: 710rpx;
  margin: 0 0 24rpx 0;
  background: #fff;
  border-radius: 20rpx;
}
.profile-menu-item {
  // width: 670rpx;
  // margin: 0 auto;
  margin: 0 24rpx 0 28rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 28rpx 0;
}
.profile-menu-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 24rpx;
}
.profile-menu-text {
  font-size: 28rpx;
  color: #262626;
  flex: 1;
}
.profile-menu-arrow {
  width: 14rpx;
  height: 24rpx;
  margin-left: 24rpx;
}

// 底部tabBar
.profile-tabbar {
  width: 750rpx;
  background: #fff;
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 10;
  display: flex;
  justify-content: space-between;
  padding: 18rpx 0 10rpx 0;
  box-shadow: 0 -2rpx 16rpx 0 rgba(0,0,0,0.04);
}
.profile-tabbar-item {
  flex: 1;
  align-items: center;
  justify-content: center;
}
.profile-tabbar-icon {
  width: 44rpx;
  height: 44rpx;
}
.profile-tabbar-text {
  font-size: 20rpx;
  color: #8c8c8c;
  text-align: center;
  margin-top: 4rpx;
}
.profile-tabbar-item--active .profile-tabbar-text,
.profile-tabbar-text--active {
  color: #ff0043;
  font-weight: 500;
}
.profile-bottom-bar-bg {
  width: 750rpx;
  height: 68rpx;
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 5;
  pointer-events: none;
}

.profile-logout-btn-wrapper {
  margin: 0 0 48rpx 0;
  display: flex;
  justify-content: center;
}
.profile-logout-btn {
  width: 100%;
  background: #fff;
  color: #ff0043;
  border: 2rpx solid #ff0043;
  border-radius: 32rpx;
  font-size: 28rpx;
  font-weight: 500;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
}

// 滚动区域样式
.profile-scroll {
  position: absolute;
  top: calc(220rpx + var(--status-bar-height)); /* 头部内容高度 */
  left: 0;
  right: 0;
  bottom: 0;
  overflow-y: auto;
  // background: #f9f9f9;
  z-index: 11;
  height: calc(100vh - 220rpx - var(--status-bar-height));
}

.profile-content {
  padding: 24rpx;
}
</style> 