<template>
  <view class="history-group">
    <!-- 日期显示 -->
    <text class="history-group__date">{{ formatTime(item.create_time, 'YYYY-MM-DD') }}</text>
    <!-- 主卡片区 -->
    <view class="history-group__maincard">
      <view class="history-group__maincard-header">
        <text class="history-group__maincard-title">{{ item.title }}</text>
        <text class="history-group__maincard-time">{{ formatTime(item.create_time) }}</text>
      </view>
      <view class="history-group__maincard-content">
        <view class="history-group__maincard-img-box" @click="handlePlayVideo">
          <image class="history-group__maincard-img" :src="item.cover_image_path_auth || defaultPoster" mode="aspectFill"/>
          <image class="history-group__maincard-play-icon" v-if="item.output_path_auth" src="@/asset/img/history/card_main.png"></image>
        </view>
        <view class="history-group__maincard-desc">
          <view class="history-group__maincard-desc-text">{{ item.user_script }}</view>
          <!-- 进度条状态显示 -->
          <view v-if="item.status !== 4 && item.status !== 5" class="history-group__maincard-status--progress">
            <view class="history-group__maincard-progress-container">
              <!-- 删除按钮 -->
              <view class="history-group__maincard-btn--delete-progress">
                <image class="history-group__maincard-delete-img" @click.stop="handleAction('delete')" src="@/asset/img/history/delete.png"></image>
              </view>
              <view class="history-group__maincard-progress-bar">
                <!-- 平滑进度动画状态 -->
                <view v-if="item.status === 0" class="history-group__maincard-progress-text--no-fill">
                  <text>{{ progressText }}</text>
                </view>
                <view v-else class="history-group__maincard-progress-fill" 
                      :style="{ 
                        width: progressWidth,
                        background: progressColor
                      }">
                  <text class="history-group__maincard-progress-text" 
                        :style="{ color: progressTextColor }">
                    {{ progressText }}
                  </text>
                </view>
              </view>
            </view>
          </view>
          <!-- 完成状态 -->
          <view v-else-if="item.status === 4" class="history-group__maincard-status--completed">
            <view class="history-group__maincard-btn--delete">
              <image class="history-group__maincard-delete-img" @click.stop="handleAction('delete')" src="@/asset/img/history/delete.png"></image>
            </view>
            <view class="history-group__maincard-btn" @click.stop="handleAction('download')">下载</view>
            <!-- <view class="history-group__maincard-btn">数据</view> -->
            <view class="history-group__maincard-btn history-group__maincard-btn--publish" @click.stop="handleAction('publish')">发布</view>
          </view>
          <!-- 失败状态 -->
          <view v-else-if="item.status === 5" class="history-group__maincard-status--completed">
            <view class="history-group__maincard-btn--delete">
              <image class="history-group__maincard-delete-img" @click.stop="handleAction('delete')" src="@/asset/img/history/delete.png"></image>
            </view>
            <view class="fail-text">生成失败</view>
            <view class="history-group__maincard-btn history-group__maincard-btn--publish" @click.stop="handleAction('retry')">重试</view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 卡片区 -->
    <!-- <view class="history-group__items" v-if="item.items && item.items.length > 0">
      <view v-for="(subItem, idx) in item.items" :key="idx" class="history-group__item">
        <image class="history-group__item-img" :src="subItem.img" mode="aspectFill" />
        <view v-if="subItem.status === 'generating'" class="history-group__item-status--generating">
          <image class="history-group__item-status-img" src="@/asset/img/history/card_image.png"></image>
          <text>图片生成中…</text>
          <text class="history-group__item-status-time">{{ subItem.estimatedTime }}</text>
        </view>
        <view v-else-if="subItem.status === 'completed'">
          <image src="@/asset/img/history/card_video.png" class="history-group__item-play-icon"></image>
        </view>
        <view v-else-if="subItem.status === 'wait'" class="history-group__item-status--wait">
          <text class="history-group__item-status-tag">待生成</text>
          <text class="history-group__item-status-count">{{ subItem.count }}张</text>
        </view>
        <view v-if="subItem.duration" class="history-group__item-duration">{{ subItem.duration }}</view>
        <image v-if="subItem.status !== 'generating'" src="@/asset/img/history/card_count_bg.png" class="history-group__item-mask"></image>
      </view>
    </view> -->
  </view>
</template>

<script setup>
import { computed } from 'vue'
import defaultPoster from '@/asset/img/history/default_poster.png'
import { formatTime } from '@/utils/tools'

const props = defineProps({
  item: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['action'])



// 进度条配置
const progressConfig = {
  0: { text: '待处理... 0%', percent: 0, width: '0%', color: '#F5F5F5', textColor: '#8C8C8C' },
  1: { text: '剪辑中... 25%', percent: 25, width: '25%', color: '#E3F2FD', textColor: '#1976D2' },
  2: { text: '剪辑中... 50%', percent: 50, width: '50%', color: '#BBDEFB', textColor: '#1565C0' },
  3: { text: '剪辑中... 75%', percent: 75, width: '75%', color: '#90CAF9', textColor: '#0D47A1' },
  4: { text: '已完成 100%', percent: 100, width: '100%', color: '#C8E6C9', textColor: '#2E7D32' },
  5: { text: '生成失败 0%', percent: 0, width: '0%', color: '#FFCDD2', textColor: '#C62828' }
}

// 进度条相关计算属性
const progressText = computed(() => {
  return progressConfig[props.item.status]?.text || '正在加载...'
})

const progressWidth = computed(() => {
  return progressConfig[props.item.status]?.width || '0%'
})

const progressColor = computed(() => {
  return progressConfig[props.item.status]?.color || '#E5E5E5'
})

const progressTextColor = computed(() => {
  return progressConfig[props.item.status]?.textColor || '#8C8C8C'
})

// 事件处理函数
function handlePlayVideo() {
  if (props.item.output_path_auth) {
    handleAction('play')
  }
}

// 统一的事件处理函数
function handleAction(actionType) {
  emit('action', {
    type: actionType,
    item: props.item
  })
}
</script>

<style lang="scss" scoped>
// 主卡片区
.history-group {
  margin-bottom: 36rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 24rpx;
  
  &__date {
    font-size: 30rpx;
    color: #262626;
    font-family: PingFangSC-Medium;
    font-weight: 500;
    margin-bottom: 20rpx;
    display: block;
    text-align: left;
  }
}

.history-group__maincard {
  background: #fff;
  border-radius: 20rpx;
  padding: 20rpx;
  margin-bottom: 24rpx;
  border-radius: 16rpx;
  border: 2rpx solid #EDEDEF;
  .history-group__maincard-header {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 18rpx;
  }
  .history-group__maincard-title {
    font-size: 28rpx;
    color: #262626;
    font-weight: 500;
    line-height: 40rpx;
    white-space: nowrap;
  }
  .history-group__maincard-time {
    font-size: 24rpx;
    color: #a8a8a8;
    line-height: 34rpx;
    white-space: nowrap;
  }
  .history-group__maincard-content {
    display: flex;
    align-items: flex-start;
    .history-group__maincard-img-box {
      position: relative;
      border-radius: 16rpx;
      width: 172rpx;
      height: 172rpx;
      overflow: hidden;
      .history-group__maincard-video {
        width: 100%;
        height: 100%;
      }
      .history-group__maincard-img {
        width: 100%;
        height: 100%;
      }
      .history-group__maincard-play-icon {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 48rpx;
        height: 48rpx;
      }
    }
    .history-group__maincard-desc {
      margin-left: 20rpx;
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      height: 172rpx; // 与图片高度一致
      
      .history-group__maincard-desc-text {
        font-size: 26rpx;
        color: #8c8c8c;
        line-height: 36rpx;
        width: 420rpx;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        height: 72rpx;
      }
      .history-group__maincard-status--progress {
        margin-left: 0;
        margin-top: 8rpx;
        width: 100%;
        
        .history-group__maincard-progress-container {
          display: flex;
          align-items: center;
          width: 100%;
          gap: 16rpx;
          
          .history-group__maincard-progress-bar {
            flex: 1;
            height: 48rpx; /* 增加高度从32rpx到48rpx */
            background-color: #FAFAFA; /* 更柔和的背景色 */
            border-radius: 24rpx; /* 增加圆角 */
            overflow: hidden;
            position: relative;
            box-shadow: none; /* 移除阴影，更加扁平 */
            border: 1rpx solid #F0F0F0; /* 添加淡色边框 */
          }
          
          .history-group__maincard-progress-fill {
            height: 100%;
            border-radius: 24rpx;
            transition: all 0.4s ease;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: none; /* 移除阴影效果，更加扁平 */
            min-width: 160rpx; /* 增加最小宽度 */
            
            &::before {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              height: 50%;
              background: linear-gradient(180deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%); /* 减少高光效果 */
              border-radius: 24rpx 24rpx 0 0;
            }
            
            &::after {
              content: '';
              position: absolute;
              top: 3rpx;
              left: 3rpx;
              right: 3rpx;
              height: 3rpx;
              background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%); /* 减少高光效果 */
              border-radius: 2rpx;
            }
            
            .history-group__maincard-progress-text {
              font-size: 22rpx; /* 增加字体大小从18rpx到22rpx */
              line-height: 30rpx;
              font-weight: 600;
              white-space: nowrap;
              text-shadow: none; /* 移除文字阴影，更加扁平 */
              z-index: 1;
              position: relative;
              letter-spacing: 0.8rpx;
              min-width: 120rpx;
              text-align: center;
              font-family: PingFangSC-Medium;
              color: inherit; /* 确保继承父元素的颜色 */
            }
          }

          .history-group__maincard-progress-text--no-fill {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2;
            height: 100%;
            
            text {
              font-size: 20rpx; /* 增加字体大小从16rpx到20rpx */
              color: #666666;
              line-height: 30rpx;
              font-weight: 500;
              white-space: nowrap;
              letter-spacing: 0.8rpx;
              font-family: PingFangSC-Regular;
            }
          }
        }
        
        .history-group__maincard-btn--delete-progress {
          width: 48rpx; /* 调整为与已完成状态一致 */
          height: 48rpx;
          background: #F7F7F7;
          border-radius: 28rpx; /* 调整为与已完成状态一致 */
          flex-shrink: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

          .history-group__maincard-delete-img {
            width: 100%; /* 调整为与已完成状态一致 */
            height: 100%;
          }
        }
      }
      .history-group__maincard-status--completed {
        margin-top: 56rpx;
        display: flex;
        flex-direction: row;
        justify-content: space-evenly;

        .history-group__maincard-btn--delete {
          width: 48rpx;
          height: 48rpx;
          background: #F7F7F7;
          border-radius: 28rpx;

          .history-group__maincard-delete-img {
            width: 100%;
            height: 100%;
          }
        }
        .history-group__maincard-btn {
          width: 108rpx;
          height: 48rpx;
          background: #F7F7F7;
          border-radius: 28rpx;
          font-weight: 400;
          font-size: 24rpx;
          color: #121215;
          text-align: center;
          line-height: 48rpx;
        }
        .history-group__maincard-btn--publish {
          color: #fff;
          background-color: #FF0043;
        }
      }
    }
  }
}

.fail-text {
  font-size: 24rpx;
  color: #ff0043;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  line-height: 48rpx;
  height: 48rpx;
  white-space: nowrap;
  align-self: center;
  display: flex;
  align-items: center;
}

// 卡片区
.history-group__items {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.history-group__item {
  width: 204rpx;
  min-height: 274rpx;
  background: #fff;
  border-radius: 14rpx;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx 0 rgba(0,0,0,0.04);
  &-img {
    position: absolute;
    inset: 0;
    width: 100%;
    height: 100%;
    border-radius: 8rpx;
    object-fit: cover;
  }
  &-status--generating {
    position: absolute;
    left: 0;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    text-align: center;
    border-radius: 0 0 0 14rpx;
    font-size: 24rpx;
    font-family: PingFangSC-Medium;
    font-weight: 500;
    color: #fff;
    padding: 0 32rpx;
    .history-group__item-status-img {
      display: block;
      width: 42rpx;
      height: 42rpx;
      margin: auto;
      margin-bottom: 8rpx;
    }
    .history-group__item-status-time {
      font-size: 20rpx;
    }
  }
  &-mask {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 44rpx;
  }
  &-play-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 44rpx;
    height: 44rpx;
  }
  &-status--wait {
    position: absolute;
    inset: 0;
    .history-group__item-status-tag {
      position: absolute;
      right: 0;
      top: 0;
      width: 78rpx;
      height: 32rpx;
      background: #FF0043;
      border-radius: 0 14rpx 0 17rpx;
      padding: 4rpx 8rpx 4rpx 10rpx;
      font-weight: 400;
      font-size: 20rpx;
      color: #FFFFFF;
      line-height: 24rpx;
    }
    .history-group__item-status-count {
      position: absolute;
      font-size: 20rpx;
      margin-left: 8rpx;
      color: #fff;
      bottom: 8rpx;
      right: 12rpx;
      z-index: 2;
    }
  }
  &-duration {
    position: absolute;
    bottom: 8rpx;
    right: 12rpx;
    color: #fff;
    font-size: 20rpx;
    z-index: 2;
    font-weight: 500;
  }
}
</style>