// 导入地址数据
import aiAreaData from "./aiArea.json";
// 导入uni-app工具函数
import { showToast } from './tools.js';
//省市区三级联动 + 中国大陆收货地址智能解析
//github地址：https://github.com/ldwonday/zh-address-parse?tdsourcetag=s_pctim_aiomsg
//集成步骤：1、下载对应js代码ES6 -> 解析成ES5（在线解析工具babel：https://babeljs.io/）；2、检查解析是否有遗漏（如 ...others 等）用法
//使用方式一：1、SetAddressList(jsonlist) 初始化省市区县;2、AddressParse(addressString) 传入地址内容返回处理识别过后的对象。
//使用方式二：1、get() 获取智能粘贴内容result（返回处理后数据：包括省市区县、详情地址）
//
//获取智能粘贴内容result（返回处理后数据：包括省市区县、详情地址）
// AddressParse("广东省河源市源城区南板桥11巷8号 陈杰凌 13510358539", 1) == {
//     area: "源城区",
//     areaCode: 441602,
//     city: "河源市",
//     cityCode: 441600,
//     detail: "南板桥11巷8号",
//     name: "陈杰凌",
//     phone: "13510358539",
//     postalCode: "",
//     province: "广东省",
//     provinceCode: 440000
// }
//使用树查找模式1
function _intelligenceClipBoardPasteCreateForOfIteratorHelper(o) {
  if (typeof Symbol === "undefined" || o[Symbol.iterator] == null) {
    if (
      Array.isArray(o) ||
      (o = _intelligenceClipBoardPasteUnsupportedIterableToArray(o))
    ) {
      var i = 0;
      var F = function F() {};
      return {
        s: F,
        n: function n() {
          if (i >= o.length) return { done: true };
          return { done: false, value: o[i++] };
        },
        e: function e(_e) {
          throw _e;
        },
        f: F,
      };
    }
    throw new TypeError(
      "Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."
    );
  }
  var it,
    normalCompletion = true,
    didErr = false,
    err;
  return {
    s: function s() {
      it = o[Symbol.iterator]();
    },
    n: function n() {
      var step = it.next();
      normalCompletion = step.done;
      return step;
    },
    e: function e(_e2) {
      didErr = true;
      err = _e2;
    },
    f: function f() {
      try {
        if (!normalCompletion && it.return != null) it.return();
      } finally {
        // eslint-disable-next-line no-unsafe-finally
        if (didErr) throw err;
      }
    },
  };
}
function _intelligenceClipBoardPasteToConsumableArray(arr) {
  return (
    _intelligenceClipBoardPasteArrayWithoutHoles(arr) ||
    _intelligenceClipBoardPasteIterableToArray(arr) ||
    _intelligenceClipBoardPasteUnsupportedIterableToArray(arr) ||
    _intelligenceClipBoardPasteNonIterableSpread()
  );
}
function _intelligenceClipBoardPasteNonIterableSpread() {
  throw new TypeError(
    "Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."
  );
}
function _intelligenceClipBoardPasteUnsupportedIterableToArray(o, minLen) {
  if (!o) return;
  if (typeof o === "string")
    return _intelligenceClipBoardPasteArrayLikeToArray(o, minLen);
  var n = Object.prototype.toString.call(o).slice(8, -1);
  if (n === "Object" && o.constructor) n = o.constructor.name;
  if (n === "Map" || n === "Set") return Array.from(o);
  if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))
    return _intelligenceClipBoardPasteArrayLikeToArray(o, minLen);
}
function _intelligenceClipBoardPasteIterableToArray(iter) {
  if (typeof Symbol !== "undefined" && Symbol.iterator in Object(iter))
    return Array.from(iter);
}
function _intelligenceClipBoardPasteArrayWithoutHoles(arr) {
  if (Array.isArray(arr))
    return _intelligenceClipBoardPasteArrayLikeToArray(arr);
}
function _intelligenceClipBoardPasteArrayLikeToArray(arr, len) {
  if (len == null || len > arr.length) len = arr.length;
  for (var i = 0, arr2 = new Array(len); i < len; i++) {
    arr2[i] = arr[i];
  }
  return arr2;
}
function _intelligenceClipBoardPasteTypeof(obj) {
  "@babel/helpers - typeof";
  if (typeof Symbol === "function" && typeof Symbol.iterator === "symbol") {
    // eslint-disable-next-line no-func-assign
    _intelligenceClipBoardPasteTypeof =
      function _intelligenceClipBoardPasteTypeof(obj) {
        return typeof obj;
      };
  } else {
    // eslint-disable-next-line no-func-assign
    _intelligenceClipBoardPasteTypeof =
      function _intelligenceClipBoardPasteTypeof(obj) {
        return obj &&
          typeof Symbol === "function" &&
          obj.constructor === Symbol &&
          obj !== Symbol.prototype
          ? "symbol"
          : typeof obj;
      };
  }
  return _intelligenceClipBoardPasteTypeof(obj);
}
function _intelligenceClipBoardPasteObjectWithoutProperties(source, excluded) {
  if (source == null) return {};
  var target = _intelligenceClipBoardPasteObjectWithoutPropertiesLoose(
    source,
    excluded
  );
  var key, i;
  if (Object.getOwnPropertySymbols) {
    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);
    for (i = 0; i < sourceSymbolKeys.length; i++) {
      key = sourceSymbolKeys[i];
      if (excluded.indexOf(key) >= 0) continue;
      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;
      target[key] = source[key];
    }
  }
  return target;
}
function _intelligenceClipBoardPasteObjectWithoutPropertiesLoose(
  source,
  excluded
) {
  if (source == null) return {};
  var target = {};
  var sourceKeys = Object.keys(source);
  var key, i;
  for (i = 0; i < sourceKeys.length; i++) {
    key = sourceKeys[i];
    if (excluded.indexOf(key) >= 0) continue;
    target[key] = source[key];
  }
  return target;
}
function _defineProperty(e, r, t) {
  return (
    (r = _toPropertyKey(r)) in e
      ? Object.defineProperty(e, r, {
          value: t,
          enumerable: !0,
          configurable: !0,
          writable: !0
        })
      : (e[r] = t),
    e
  );
}
function _toPropertyKey(t) {
  var i = _toPrimitive(t, "string");
  return "symbol" == _intelligenceClipBoardPasteTypeof(i) ? i : i + "";
}
function _toPrimitive(t, r) {
  if ("object" != _intelligenceClipBoardPasteTypeof(t) || !t) return t;
  var e = t[Symbol.toPrimitive];
  if (void 0 !== e) {
    var i = e.call(t, r || "default");
    if ("object" != _intelligenceClipBoardPasteTypeof(i)) return i;
    throw new TypeError("@@toPrimitive must return a primitive value.");
  }
  return ("string" === r ? String : Number)(t);
}
function ownKeys(e, r) {
  var t = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var o = Object.getOwnPropertySymbols(e);
    r &&
      (o = o.filter(function (r) {
        return Object.getOwnPropertyDescriptor(e, r).enumerable;
      })),
      t.push.apply(t, o);
  }
  return t;
}
function _objectSpread(e) {
  for (var r = 1; r < arguments.length; r++) {
    var t = null != arguments[r] ? arguments[r] : {};
    r % 2
      ? ownKeys(Object(t), !0).forEach(function (r) {
          _defineProperty(e, r, t[r]);
        })
      : Object.getOwnPropertyDescriptors
      ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t))
      : ownKeys(Object(t)).forEach(function (r) {
          Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
  }
  return e;
}

var namesJson = [
  "赵",
  "钱",
  "孙",
  "李",
  "周",
  "吴",
  "郑",
  "王",
  "冯",
  "陈",
  "楮",
  "卫",
  "蒋",
  "沈",
  "韩",
  "杨",
  "朱",
  "秦",
  "尤",
  "许",
  "何",
  "吕",
  "施",
  "张",
  "孔",
  "曹",
  "严",
  "华",
  "金",
  "魏",
  "陶",
  "姜",
  "戚",
  "谢",
  "邹",
  "喻",
  "柏",
  "水",
  "窦",
  "章",
  "云",
  "苏",
  "潘",
  "葛",
  "奚",
  "范",
  "彭",
  "郎",
  "鲁",
  "韦",
  "昌",
  "马",
  "苗",
  "凤",
  "花",
  "方",
  "俞",
  "任",
  "袁",
  "柳",
  "酆",
  "鲍",
  "史",
  "唐",
  "费",
  "廉",
  "岑",
  "薛",
  "雷",
  "贺",
  "倪",
  "汤",
  "滕",
  "殷",
  "罗",
  "毕",
  "郝",
  "邬",
  "安",
  "常",
  "乐",
  "于",
  "时",
  "傅",
  "皮",
  "卞",
  "齐",
  "康",
  "伍",
  "余",
  "元",
  "卜",
  "顾",
  "孟",
  "平",
  "黄",
  "和",
  "穆",
  "萧",
  "尹",
  "姚",
  "邵",
  "湛",
  "汪",
  "祁",
  "毛",
  "禹",
  "狄",
  "米",
  "贝",
  "明",
  "臧",
  "计",
  "伏",
  "成",
  "戴",
  "谈",
  "宋",
  "茅",
  "庞",
  "熊",
  "纪",
  "舒",
  "屈",
  "项",
  "祝",
  "董",
  "梁",
  "杜",
  "阮",
  "蓝",
  "闽",
  "席",
  "季",
  "麻",
  "强",
  "贾",
  "路",
  "娄",
  "危",
  "江",
  "童",
  "颜",
  "郭",
  "梅",
  "盛",
  "林",
  "刁",
  "锺",
  "徐",
  "丘",
  "骆",
  "高",
  "夏",
  "蔡",
  "田",
  "樊",
  "胡",
  "凌",
  "霍",
  "虞",
  "万",
  "支",
  "柯",
  "昝",
  "管",
  "卢",
  "莫",
  "经",
  "房",
  "裘",
  "缪",
  "干",
  "解",
  "应",
  "宗",
  "丁",
  "宣",
  "贲",
  "邓",
  "郁",
  "单",
  "杭",
  "洪",
  "包",
  "诸",
  "左",
  "石",
  "崔",
  "吉",
  "钮",
  "龚",
  "程",
  "嵇",
  "邢",
  "滑",
  "裴",
  "陆",
  "荣",
  "翁",
  "荀",
  "羊",
  "於",
  "惠",
  "甄",
  "麹",
  "家",
  "封",
  "芮",
  "羿",
  "储",
  "靳",
  "汲",
  "邴",
  "糜",
  "松",
  "井",
  "段",
  "富",
  "巫",
  "乌",
  "焦",
  "巴",
  "弓",
  "牧",
  "隗",
  "山",
  "谷",
  "车",
  "侯",
  "宓",
  "蓬",
  "全",
  "郗",
  "班",
  "仰",
  "秋",
  "仲",
  "伊",
  "宫",
  "宁",
  "仇",
  "栾",
  "暴",
  "甘",
  "斜",
  "厉",
  "戎",
  "祖",
  "武",
  "符",
  "刘",
  "景",
  "詹",
  "束",
  "龙",
  "叶",
  "幸",
  "司",
  "韶",
  "郜",
  "黎",
  "蓟",
  "薄",
  "印",
  "宿",
  "白",
  "怀",
  "蒲",
  "邰",
  "从",
  "鄂",
  "索",
  "咸",
  "籍",
  "赖",
  "卓",
  "蔺",
  "屠",
  "蒙",
  "池",
  "乔",
  "阴",
  "郁",
  "胥",
  "能",
  "苍",
  "双",
  "闻",
  "莘",
  "党",
  "翟",
  "谭",
  "贡",
  "劳",
  "逄",
  "姬",
  "申",
  "扶",
  "堵",
  "冉",
  "宰",
  "郦",
  "雍",
  "郤",
  "璩",
  "桑",
  "桂",
  "濮",
  "牛",
  "寿",
  "通",
  "边",
  "扈",
  "燕",
  "冀",
  "郏",
  "浦",
  "尚",
  "农",
  "温",
  "别",
  "庄",
  "晏",
  "柴",
  "瞿",
  "阎",
  "充",
  "慕",
  "连",
  "茹",
  "习",
  "宦",
  "艾",
  "鱼",
  "容",
  "向",
  "古",
  "易",
  "慎",
  "戈",
  "廖",
  "庾",
  "终",
  "暨",
  "居",
  "衡",
  "步",
  "都",
  "耿",
  "满",
  "弘",
  "匡",
  "国",
  "文",
  "寇",
  "广",
  "禄",
  "阙",
  "东",
  "欧",
  "殳",
  "沃",
  "利",
  "蔚",
  "越",
  "夔",
  "隆",
  "师",
  "巩",
  "厍",
  "聂",
  "晁",
  "勾",
  "敖",
  "融",
  "冷",
  "訾",
  "辛",
  "阚",
  "那",
  "简",
  "饶",
  "空",
  "曾",
  "毋",
  "沙",
  "乜",
  "养",
  "鞠",
  "须",
  "丰",
  "巢",
  "关",
  "蒯",
  "相",
  "查",
  "后",
  "荆",
  "红",
  "游",
  "竺",
  "权",
  "逑",
  "盖",
  "益",
  "桓",
  "公",
  "万俟",
  "司马",
  "上官",
  "欧阳",
  "夏侯",
  "诸葛",
  "闻人",
  "东方",
  "赫连",
  "皇甫",
  "尉迟",
  "公羊",
  "澹台",
  "公冶",
  "宗政",
  "濮阳",
  "淳于",
  "单于",
  "太叔",
  "申屠",
  "公孙",
  "仲孙",
  "轩辕",
  "令狐",
  "锺离",
  "宇文",
  "长孙",
  "慕容",
  "鲜于",
  "闾丘",
  "司徒",
  "司空",
  "丌官",
  "司寇",
  "仉",
  "督",
  "子车",
  "颛孙",
  "端木",
  "巫马",
  "公西",
  "漆雕",
  "乐正",
  "壤驷",
  "公良",
  "拓拔",
  "夹谷",
  "宰父",
  "谷梁",
  "晋",
  "楚",
  "阎",
  "法",
  "汝",
  "鄢",
  "涂",
  "钦",
  "段干",
  "百里",
  "东郭",
  "南门",
  "呼延",
  "归",
  "海",
  "羊舌",
  "微生",
  "岳",
  "帅",
  "缑",
  "亢",
  "况",
  "后",
  "有",
  "琴",
  "梁丘",
  "左丘",
  "东门",
  "西门",
  "商",
  "牟",
  "佘",
  "佴",
  "伯",
  "赏",
  "南宫",
  "墨",
  "哈",
  "谯",
  "笪",
  "年",
  "爱",
  "阳",
  "佟",
  "第五",
  "言",
  "福",
  "泊",
];
var provinces = [];
var cities = [];
var areas = [];
var provinceString = "";
var cityString = "";
var areaString = ""; // 设置地址

var SetAddressList = function SetAddressList(addressJson) {
  provinces = addressJson.reduce(function (per, cur) {
    var children = cur.children,
      others = _intelligenceClipBoardPasteObjectWithoutProperties(cur, [
        "children",
      ]);
    return per.concat(others);
  }, []);
  cities = addressJson.reduce(function (per, cur) {
    return per.concat(
      cur.children
        ? cur.children.map(function (_ref) {
            var children = _ref.children,
              others = _intelligenceClipBoardPasteObjectWithoutProperties(
                _ref,
                ["children"]
              );
            return _objectSpread(
              _objectSpread({}, others),
              {},
              {
                provinceCode: cur.id,
              }
            )
          })
        : []
    );
  }, []);
  areas = addressJson.reduce(function (per, cur) {
    var provinceCode = cur.id;
    return per.concat(
      cur.children
        ? cur.children.reduce(function (p, c) {
            var cityCode = c.id;
            return p.concat(
              c.children
                ? c.children.map(function (_ref2) {
                    var children = _ref2.children,
                      others =
                        _intelligenceClipBoardPasteObjectWithoutProperties(
                          _ref2,
                          ["children"]
                        );
                    return _objectSpread(
                      _objectSpread({}, others),
                      {},
                      {
                        cityCode: cityCode,
                        provinceCode: provinceCode
                      }
                    );
                  })
                : []
            );
          }, [])
        : []
    );
  }, []);
  provinceString = JSON.stringify(provinces);
  cityString = JSON.stringify(cities);
  areaString = JSON.stringify(areas);
};

/**
 * 需要解析的地址，type是解析的方式，默认是正则匹配
 * @param address
 * @param options?：type： 0:正则，1：树查找, textFilter： 清洗的字段
 * @returns {{}|({area: Array, province: Array, phone: string, city: Array, name: string, detail: Array} & {area: (*|string), province: (*|string), city: (*|string), detail: (Array|boolean|string|string)})}
 * @constructor
 */
var AddressParse = function AddressParse(address, options) {
  // 这里先手动删除输入的空格避免名称识别错误
  address = address.replace(/\s/g, "");
  var _ref3 =
      _intelligenceClipBoardPasteTypeof(options) === "object"
        ? options
        : typeof options === "number"
        ? {
            type: options,
          }
        : {},
    _ref3$type = _ref3.type,
    type = _ref3$type === void 0 ? 0 : _ref3$type,
    _ref3$textFilter = _ref3.textFilter,
    textFilter = _ref3$textFilter === void 0 ? [] : _ref3$textFilter,
    _ref3$nameMaxLength = _ref3.nameMaxLength,
    nameMaxLength = _ref3$nameMaxLength === void 0 ? 4 : _ref3$nameMaxLength;
  if (!address) {
    return {};
  } // 解析结果

  var parseResult = {
    phone: "",
    province: [],
    city: [],
    area: [],
    detail: [],
    name: "",
  }; // 【1】对地址的关键字进行过滤 空格合并等操作

  address = cleanAddress(address, textFilter); // 【2】识别手机号 存放到parseResult.phone 返回剩余的地址继续解析

  var resultPhone = filterPhone(address);
  parseResult.phone = resultPhone.phone;
  address = resultPhone.address; // 【3】进行邮编解析 存放到resultCode.postalCode 返回剩余的地址继续解析

  var resultCode = filterPostalCode(address);
  parseResult.postalCode = resultCode.postalCode;
  address = resultCode.address; // 【4】此时地址字符串剩下姓名和省市区 正则\s+切割

  var splitAddress = address
    .split(/,|，|\s+/)
    .filter(function (item) {
      return item;
    })
    .map(function (item) {
      return item.trim();
    }); // 【5】找省市区和详细地址

  // 暂时注释 用于修复安卓手机客户名称与地名冲突导致复制粘贴智能识别地址不对 现在修改为安卓IOS同一逻辑
  // if(!ionic.Platform.isAndroid()){
  //     splitAddress.sort(function(a,b){
  //         return a.length > b.length ? -1 : 1;
  //     })
  // }
  splitAddress.sort(function (a, b) {
    return a.length > b.length ? -1 : 1;
  });

  splitAddress.forEach(function (item, index) {
    // 识别地址
    if (
      !parseResult.province[0] ||
      !parseResult.city[0] ||
      !parseResult.area[0]
    ) {
      // 两个方法都可以解析，正则和树查找
      var parse = {};
      type === 1 && (parse = parseRegion(item, parseResult));
      type === 0 && (parse = parseRegionWithRegexp(item, parseResult));
      var _parse = parse,
        _province = _parse.province,
        _city = _parse.city,
        _area = _parse.area,
        _detail = _parse.detail;
      parseResult.province = _province || [];
      parseResult.area = _area || [];
      parseResult.city = _city || [];
      parseResult.detail = parseResult.detail.concat(_detail || []);
    } else {
      parseResult.detail.push(item);
    }
  });

  var province = parseResult.province[0];
  var city = parseResult.city[0];
  var area = parseResult.area[0];
  var detail = parseResult.detail; // 地址都解析完了，姓名应该是在详细地址里面
  if (detail && detail.length > 0) {
    var copyDetail = _intelligenceClipBoardPasteToConsumableArray(detail);
    copyDetail.sort(function (a, b) {
      return a.length - b.length;
    }); // 排序后从最短的开始找名字，没找到的话就看第一个是不是咯

    var index = copyDetail.findIndex(function (item) {
      return judgeFragmentIsName(item, nameMaxLength);
    });

    var name = "";
    if (index !== -1) {
      name = copyDetail[index];
    } else if (
      copyDetail[0].length <= nameMaxLength &&
      /[\u4E00-\u9FA5]/.test(copyDetail[0])
    ) {
      name = copyDetail[0];
    } // 找到了名字就从详细地址里面删除它

    if (name) {
      parseResult.name = name;
      detail.splice(
        detail.findIndex(function (item) {
          return item === name;
        }),
        1
      );
    }
  }

  return Object.assign(parseResult, {
    province: (province && province.areaName) || "",
    provinceCode: (province && province.id) || "",
    city: (city && city.areaName) || "",
    cityCode: (city && city.id) || "",
    area: (area && area.areaName) || "",
    areaCode: (area && area.id) || "",
    detail: (detail && detail.length > 0 && detail.join("")) || "",
  });
};

/**
 * 利用正则表达式解析
 * @param fragment
 * @param hasParseResult
 * @returns {{area: (Array|*|string), province: (Array|*|string), city: (Array|*|string|string), detail: (*|Array)}}
 */
var parseRegionWithRegexp = function parseRegionWithRegexp(
  fragment,
  hasParseResult
) {
  var province = hasParseResult.province || [],
    city = hasParseResult.city || [],
    area = hasParseResult.area || [],
    detail = [];
  var matchStr = "";

  if (province.length === 0) {
    for (var i = 1; i < fragment.length; i++) {
      var str = fragment.substring(0, i + 1);
      var regexProvince = new RegExp(
        '{"code":"[0-9]{1,6}","name":"'.concat(str, '[\u4E00-\u9FA5]*?"}'),
        "g"
      );
      var matchProvince = provinceString.match(regexProvince);
      if (matchProvince) {
        var provinceObj = JSON.parse(matchProvince[0]);
        if (matchProvince.length === 1) {
          province = [];
          matchStr = str;
          province.push(provinceObj);
        }
      } else {
        break;
      }
    }
    if (province[0]) {
      fragment = fragment.replace(new RegExp(matchStr), "");
    }
  }
  if (city.length === 0) {
    for (var _i = 1; _i < fragment.length; _i++) {
      var _str = fragment.substring(0, _i + 1);
      var regexCity = new RegExp(
        '{"code":"[0-9]{1,6}","name":"'
          .concat(_str, '[\u4E00-\u9FA5]*?","provinceCode":"')
          .concat(province[0] ? "".concat(province[0].id) : "[0-9]{1,6}", '"}'),
        "g"
      );
      var matchCity = cityString.match(regexCity);
      if (matchCity) {
        var cityObj = JSON.parse(matchCity[0]);
        if (matchCity.length === 1) {
          city = [];
          matchStr = _str;
          city.push(cityObj);
        }
      } else {
        break;
      }
    }
    if (city[0]) {
      var provinceCode = city[0].provinceCode;
      fragment = fragment.replace(new RegExp(matchStr), "");
      if (province.length === 0) {
        var _regexProvince = new RegExp(
          '{"code":"'.concat(provinceCode, '","name":"[\u4E00-\u9FA5]+?"}'),
          "g"
        );
        var _matchProvince = provinceString.match(_regexProvince);
        province.push(JSON.parse(_matchProvince[0]));
      }
    }
  }
  if (area.length === 0) {
    for (var _i2 = 1; _i2 < fragment.length; _i2++) {
      var _str2 = fragment.substring(0, _i2 + 1);
      var regexArea = new RegExp(
        '{"code":"[0-9]{1,6}","name":"'
          .concat(_str2, '[\u4E00-\u9FA5]*?","cityCode":"')
          .concat(city[0] ? city[0].id : "[0-9]{1,6}", '","provinceCode":"')
          .concat(province[0] ? "".concat(province[0].id) : "[0-9]{1,6}", '"}'),
        "g"
      );
      var matchArea = areaString.match(regexArea);
      if (matchArea) {
        var areaObj = JSON.parse(matchArea[0]);
        if (matchArea.length === 1) {
          area = [];
          matchStr = _str2;
          area.push(areaObj);
        }
      } else {
        break;
      }
    }

    if (area[0]) {
      var _area$ = area[0],
        _provinceCode = _area$.provinceCode,
        cityCode = _area$.cityCode;
      fragment = fragment.replace(matchStr, "");
      if (province.length === 0) {
        var _regexProvince2 = new RegExp(
          '{"code":"'.concat(_provinceCode, '","name":"[\u4E00-\u9FA5]+?"}'),
          "g"
        );
        var _matchProvince2 = provinceString.match(_regexProvince2);
        province.push(JSON.parse(_matchProvince2[0]));
      }
      if (city.length === 0) {
        var _regexCity = new RegExp(
          '{"code":"'
            .concat(cityCode, '","name":"[\u4E00-\u9FA5]+?","provinceCode":"')
            .concat(_provinceCode, '"}'),
          "g"
        );
        var _matchCity = cityString.match(_regexCity);
        city.push(JSON.parse(_matchCity[0]));
      }
    }
  } // 解析完省市区如果还存在地址，则默认为详细地址

  if (fragment.length > 0) {
    detail.push(fragment);
  }

  return {
    province: province,
    city: city,
    area: area,
    detail: detail,
  };
};

/**
 * 利用树向下查找解析
 * @param fragment
 * @param hasParseResult
 * @returns {{area: Array, province: Array, city: Array, detail: Array}}
 */
var parseRegion = function parseRegion(fragment, hasParseResult) {
  var province = [],
    city = [],
    area = [],
    detail = [];
  if (hasParseResult.province[0]) {
    province = hasParseResult.province;
  } else {
    // 从省开始查找
    var _iterator =
        _intelligenceClipBoardPasteCreateForOfIteratorHelper(provinces),
      _step;
    try {
      for (_iterator.s(); !(_step = _iterator.n()).done; ) {
        var tempProvince = _step.value;
        var name = tempProvince.areaName;
        var replaceName = "";

        for (var i = name.length; i > 1; i--) {
          var temp = name.substring(0, i);
          if (fragment.indexOf(temp) === 0) {
            replaceName = temp;
            break;
          }
        }
        if (replaceName) {
          province.push(tempProvince);
          fragment = fragment.replace(new RegExp(replaceName), "");
          break;
        }
      }
    } catch (err) {
      _iterator.e(err);
    } finally {
      _iterator.f();
    }
  }

  if (hasParseResult.city[0]) {
    city = hasParseResult.city;
  } else {
    // 从市区开始查找
    var _iterator2 =
        _intelligenceClipBoardPasteCreateForOfIteratorHelper(cities),
      _step2;

    try {
      var _loop = function _loop() {
        var tempCity = _step2.value;
        var name = tempCity.areaName,
          provinceCode = tempCity.provinceCode;
        var currentProvince = province[0]; // 有省

        if (currentProvince) {
          if (currentProvince.id === provinceCode) {
            var _replaceName = "";
            for (var _i3 = name.length; _i3 > 1; _i3--) {
              var _temp = name.substring(0, _i3);
              if (fragment.indexOf(_temp) === 0) {
                _replaceName = _temp;
                break;
              }
            }
            if (_replaceName) {
              city.push(tempCity);
              fragment = fragment.replace(new RegExp(_replaceName), "");
              return "break";
            }
          }
        } else {
          // 没有省，市不可能重名
          for (var _i4 = name.length; _i4 > 1; _i4--) {
            var _replaceName2 = name.substring(0, _i4);
            if (fragment.indexOf(_replaceName2) === 0) {
              city.push(tempCity);
              province.push(
                provinces.find(function (item) {
                  return item.id === provinceCode;
                })
              );
              fragment = fragment.replace(_replaceName2, "");
              break;
            }
          }
          if (city.length > 0) {
            return "break";
          }
        }
      };
      for (_iterator2.s(); !(_step2 = _iterator2.n()).done; ) {
        var _ret = _loop();
        if (_ret === "break") break;
      }
    } catch (err) {
      _iterator2.e(err);
    } finally {
      _iterator2.f();
    }
  } // 从区市县开始查找

  var _iterator3 = _intelligenceClipBoardPasteCreateForOfIteratorHelper(areas),
    _step3;
  try {
    var _loop2 = function _loop2() {
      var tempArea = _step3.value;
      var name = tempArea.areaName,
        provinceCode = tempArea.provinceCode,
        cityCode = tempArea.cityCode;
      var currentProvince = province[0];
      var currentCity = city[0]; // 有省或者市

      if (currentProvince || currentCity) {
        if (
          (currentProvince &&
            currentProvince.id === provinceCode &&
            currentCity &&
            currentCity.id === cityCode) ||
          !currentCity
        ) {
          var _replaceName3 = "";
          for (var _i5 = name.length; _i5 > 1; _i5--) {
            var _temp2 = name.substring(0, _i5);
            if (fragment.indexOf(_temp2) === 0) {
              _replaceName3 = _temp2;
              break;
            }
          }
          if (_replaceName3) {
            area.push(tempArea);
            !currentCity &&
              city.push(
                cities.find(function (item) {
                  return item.id === cityCode;
                })
              );
            !currentProvince &&
              province.push(
                provinces.find(function (item) {
                  return item.id === provinceCode;
                })
              );
            fragment = fragment.replace(_replaceName3, "");
            return "break";
          }
        }
      } else {
        // 没有省市，区县市有可能重名，这里暂时不处理，因为概率极低，可以根据添加市解决
        for (var _i6 = name.length; _i6 > 1; _i6--) {
          var _replaceName4 = name.substring(0, _i6);
          if (fragment.indexOf(_replaceName4) === 0) {
            area.push(tempArea);
            city.push(
              cities.find(function (item) {
                return item.id === cityCode;
              })
            );
            province.push(
              provinces.find(function (item) {
                return item.id === provinceCode;
              })
            );
            fragment = fragment.replace(_replaceName4, "");
            break;
          }
        }
        if (area.length > 0) {
          return "break";
        }
      }
    };
    for (_iterator3.s(); !(_step3 = _iterator3.n()).done; ) {
      var _ret2 = _loop2();
      if (_ret2 === "break") break;
    } // 解析完省市区如果还存在地址，则默认为详细地址
  } catch (err) {
    _iterator3.e(err);
  } finally {
    _iterator3.f();
  }

  if (fragment.length > 0) {
    detail.push(fragment);
  }

  return {
    province: province,
    city: city,
    area: area,
    detail: detail,
  };
};

/**
 * 判断是否是名字
 * @param fragment
 * @returns {string}
 */
var judgeFragmentIsName = function judgeFragmentIsName(
  fragment,
  nameMaxLength
) {
  if (!fragment || !/[\u4E00-\u9FA5]/.test(fragment)) {
    return "";
  } // 如果包含下列称呼，则认为是名字，可自行添加

  var nameCall = [
    "先生",
    "小姐",
    "同志",
    "哥哥",
    "姐姐",
    "妹妹",
    "弟弟",
    "妈妈",
    "爸爸",
    "爷爷",
    "奶奶",
    "姑姑",
    "舅舅",
  ];
  if (
    nameCall.find(function (item) {
      return fragment.indexOf(item) !== -1;
    })
  ) {
    return fragment;
  } // 如果百家姓里面能找到这个姓，并且长度在1-5之间

  var nameFirst = fragment.substring(0, 1);
  if (
    fragment.length <= nameMaxLength &&
    fragment.length > 1 &&
    namesJson.indexOf(nameFirst) !== -1
  ) {
    return fragment;
  }
  return "";
};

/**
 * 匹配电话
 * @param address
 * @returns {{address: *, phone: string}}
 */
var filterPhone = function filterPhone(address) {
  var phone = ""; // 整理电话格式
  address = address.replace(/(\d{3})-(\d{4})-(\d{4})/g, "$1$2$3");
  address = address.replace(/(\d{3}) (\d{4}) (\d{4})/g, "$1$2$3");
  address = address.replace(/(\d{4}) \d{4} \d{4}/g, "$1$2$3");
  address = address.replace(/(\d{4})/g, "$1");
  var mobileReg =
    /(\d{7,12})|(\d{3,4}-\d{6,8})|(86-[1][0-9]{10})|(86[1][0-9]{10})|([1][0-9]{10})/g;
  var mobile = mobileReg.exec(address);
  if (mobile) {
    phone = mobile[0];
    address = address.replace(mobile[0], " ");
  }
  return {
    address: address,
    phone: phone,
  };
};

/**
 * 匹配邮编
 * @param address
 * @returns {{address: *, postalCode: string}}
 */
var filterPostalCode = function filterPostalCode(address) {
  var postalCode = "";
  var postalCodeReg = /\d{6}/g;
  var code = postalCodeReg.exec(address);
  if (code) {
    postalCode = code[0];
    address = address.replace(code[0], " ");
  }
  return {
    address: address,
    postalCode: postalCode,
  };
};

/**
 * 地址清洗
 * @param address
 * @returns {*}
 */
var cleanAddress = function cleanAddress(address) {
  var textFilter =
    arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];
  // 去换行等
  address = address
    .replace(/\r\n/g, " ")
    .replace(/\n/g, " ")
    .replace(/\t/g, " "); // 自定义去除关键字，可自行添加
  // 自定义去除关键字，可自行添加
  var search = [
    "详细地址",
    "收货地址",
    "收件地址",
    "地址",
    "所在地区",
    "地区",
    "姓名",
    "收货人",
    "收件人",
    "联系人",
    "收",
    "邮编",
    "联系电话",
    "电话",
    "联系人手机号码",
    "手机号码",
    "手机号",
  ];
  search = search.concat(textFilter);
  search.forEach(function (str) {
    address = address.replace(new RegExp(str, "g"), " ");
  }); // 多个空格replace为一个
  // 去除多余的符号
  var pattern = new RegExp(
    "[`~!@#$^&*()=|{}':;',\\[\\].<>/?~！@#￥……&*（）——|{}【】‘；：”“’。，、？]",
    "g"
  );
  address = address.replace(pattern, " ");
  address = address.replace(/ {2,}/g, " ");
  return address;
};

//获取智能粘贴内容（返回处理后数据：包括省市区县、详情地址）
// {
//     area: "源城区",
//     areaCode: 441602,
//     city: "河源市",
//     cityCode: 441600,
//     detail: "南板桥11巷8号",
//     name: "陈杰凌",
//     phone: "13510358539",
//     postalCode: "",
//     province: "广东省",
//     provinceCode: 440000
// }
var get = function () {
  return new Promise((resolve, reject) => {
    // 使用uni-app的剪贴板API
    if (uni.getClipboardData) {
      uni.getClipboardData({
        success: (res) => {
          if (res.data && res.data !== "") {
            resolve(AddressParse(res.data, 1));
          } else {
            showToast("请复制收件地址再粘贴，会自动拆解收件人姓名、电话、地址信息。");
            reject(new Error("剪贴板内容为空"));
          }
        },
        fail: (err) => {
          showToast("请复制收件地址再粘贴，会自动拆解收件人姓名、电话、地址信息。");
          reject(err);
        }
      });
    } else {
      showToast("智能粘贴功能仅在Android与IOS设备上生效！");
      // 返回示例数据用于测试
      resolve(AddressParse("邱志亮 广东省 桑达科技大厦1203 18270777302", 1));
    }
  });
};
// 自动初始化地址数据
SetAddressList(aiAreaData);

export default {
  AddressParse,
  SetAddressList,
  get,
};
