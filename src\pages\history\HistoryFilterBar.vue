<template>
  <view class="history-header-filters">
    <!-- 日期选择器 -->
    <view class="history-header-filter-picker">
      <uni-datetime-picker
        type="daterange"
        v-model="dateRange"
        @change="onDateChange"
        @maskClick="onMaskClick"
        @show="onDatePickerShow"
        :clear-icon="false"
        class="custom-datetime-picker"
      />
      <image class="history-header-filter-icon" :src="tabNoselect" />
    </view>
    <!-- 状态筛选 -->
    <view class="history-header-filter-wrapper">
      <view :class="['history-header-filter', currentStatus !== null ? 'history-header-filter--active' : '']" @click="showStatusDropdown = !showStatusDropdown">
        <text>{{ statusLabel }}</text>
        <image class="history-header-filter-icon" :src="currentStatus !== null ? tabSelected : tabNoselect" />
      </view>
      <view v-if="showStatusDropdown" class="status-dropdown">
        <view class="status-dropdown-item" :class="{ 'status-dropdown-item--active': currentStatus === null }" @click="onStatusChange(null)">全部</view>
        <view class="status-dropdown-item" v-for="item in statusOptions.slice(1)" :key="item.value" :class="{ 'status-dropdown-item--active': currentStatus == item.value }" @click="onStatusChange(item.value)">{{ item.label }}</view>
      </view>
      <view v-if="showStatusDropdown" class="status-dropdown-mask" @click="showStatusDropdown = false"></view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
// 手动导入 uni-datetime-picker 组件
import UniDatetimePicker from '@dcloudio/uni-ui/lib/uni-datetime-picker/uni-datetime-picker.vue'
import tabSelected from '@/asset/img/history/tab_selected.png'
import tabNoselect from '@/asset/img/history/tab_noselect.png'

// 获取今天和7天前的日期字符串
function getDateStr(date) {
  return date.toISOString().slice(0, 10)
}
const today = new Date()
const sevenDaysAgo = new Date()
sevenDaysAgo.setDate(today.getDate() - 6) // 包含今天共7天

// 默认的状态选项
const statusOptions = [
  { label: '全部', value: null },
  { label: '待处理', value: 0 },
  { label: '剪辑中', value: 3 },
  { label: '生成失败', value: 5 },
  { label: '已完成', value: 4 },
]

const emit = defineEmits(['filter'])

// 内部状态
const currentDate = getDateStr(today)
const dateRange = ref([getDateStr(sevenDaysAgo), currentDate])
const currentStatus = ref(null)
const showStatusDropdown = ref(false)

// 计算当前状态的标签
const statusLabel = computed(() => {
  const found = statusOptions.find(i => i.value === currentStatus.value)
  return found ? found.label : '全部'
})

// 日期选择器确认
function onDateChange(val) {
  if (val && val.length === 2) {
    dateRange.value = val
    emitFilter()
  }
  uni.showTabBar()
}

// 状态选择
function onStatusChange(val) {
  if (currentStatus.value === val) {
    showStatusDropdown.value = false
    return
  }
  
  currentStatus.value = val
  showStatusDropdown.value = false
  emitFilter()
}

// 发送筛选事件
function emitFilter() {
  emit('filter', {
    dateRange: dateRange.value,
    status: currentStatus.value
  })
}

// 日期选择器打开
function onDatePickerShow() {
  uni.hideTabBar()
}

// 日期选择器关闭
function onMaskClick() {
  uni.showTabBar()
}

// 对外暴露当前筛选值的方法
defineExpose({
  getFilters: () => ({
    dateRange: dateRange.value,
    status: currentStatus.value
  })
})
</script>

<style lang="scss" scoped>
.history-header-filters {
  background: transparent;
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: center;
  margin-top: 24rpx;
  margin-bottom: 32rpx;
  padding-left: 32rpx;
  gap: 24rpx;
}

.history-header-filter {
  background: #fff;
  border-radius: 8rpx;
  padding: 6rpx 0rpx 6rpx 20rpx;
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color:#595959;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  &--active {
    color: #ff0043;
  }
}

.history-header-filter-wrapper {
  position: relative;
  display: inline-block;
}

.history-header-filter-picker {
  display: flex;
  align-items: center;
  position: relative;
  background-color: #fff;
}

.history-header-filter-icon {
  width: 20rpx;
  height: 12rpx;
  margin-left: 8rpx;
  flex-shrink: 0;
  margin-right: 20rpx;
}

.custom-datetime-picker {
  flex: 1;
  display: flex;
  align-items: center;
  background: #fff;
  border-radius: 8rpx;
  border: none !important;
  height: 56rpx;
  min-width: 220rpx;
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #8A8A8A;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  box-sizing: border-box;
  padding: 0 10rpx;
  transition: all 0.2s;
  box-shadow: none !important;

  ::v-deep .uni-date-x--border {
    border: none !important;
  }

  ::v-deep .uni-date-range .uni-icons {
    font-size: 38rpx!important;
    height: 56rpx;
  }

  ::v-deep .uni-date__x-input{
    height: 56rpx;
    line-height: 56rpx;
    font-size: 28rpx;
  }

  ::v-deep .range-separator {
    height: 56rpx;
    line-height: 56rpx;
  }

  // uni-datetime-picker 自定义样式覆盖
  ::v-deep .uni-datetime-picker {
    .uni-date-x--border {
      border: none !important;
    }

    .uni-date__x-input {
      height: 56rpx;
      line-height: 56rpx;
    }

    .range-separator {
      height: 56rpx;
      line-height: 56rpx;
    }
  }

  // uni-datetime-picker 日历弹窗样式覆盖
  ::v-deep .uni-calendar {
    // 确认按钮样式 - 这是最重要的按钮
    .uni-datetime-picker--btn {
      background-color: #FF0043 !important;
      color: #fff !important;
      border-radius: 8rpx !important;
    }
    
    // 月份文字颜色
    .uni-calendar__button-text {
      color: #FF0043 !important;
    }
  }

  // 日历项目样式需要在 calendar-item 组件中覆盖
  ::v-deep .uni-calendar-item--hook {
    // 选中日期的样式
    .uni-calendar__date--checked {
      background-color: #FF0043 !important;
      color: #fff !important;
    }
    
    // 范围选择中间日期的样式
    .uni-calendar__date--multiple {
      background-color: rgba(255, 0, 67, 0.1) !important;
    }
    
    // 今天的标记
    .uni-calendar__date--today {
      color: #FF0043 !important;
    }
    
    // hover状态
    .uni-calendar__date--hover {
      background-color: rgba(255, 0, 67, 0.1) !important;
    }
  }

  // 如果还有其他相关的日历样式类
  ::v-deep .uni-calendar__date {
    &.uni-calendar__date--checked {
      background-color: #FF0043 !important;
      color: #fff !important;
    }
    
    &.uni-calendar__date--multiple {
      background-color: rgba(255, 0, 67, 0.1) !important;
    }
    
    &.uni-calendar__date--today {
      color: #FF0043 !important;
    }
  }

  // 移动端日历样式
  ::v-deep .uni-calendar--fixed {
    .uni-calendar__date--checked {
      background-color: #FF0043 !important;
    }

    .uni-calendar__date--multiple {
      background-color: rgba(255, 0, 67, 0.1) !important;
    }

    .uni-calendar__confirm {
      background-color: #FF0043 !important;
    }
  }

  // 更强力的选中日期样式覆盖
  ::v-deep .uni-calendar__date {
    &.uni-calendar__date--checked {
      background-color: #FF0043 !important;
      color: #fff !important;
    }
    
    &.uni-calendar__date--multiple {
      background-color: rgba(255, 0, 67, 0.1) !important;
    }
    
    &.uni-calendar__date--today {
      color: #FF0043 !important;
    }
  }

  // 针对移动端弹窗的特殊处理
  ::v-deep .uni-calendar {
    .uni-calendar__date.uni-calendar__date--checked {
      background-color: #FF0043 !important;
      color: #ffffff !important;
    }
    
    .uni-calendar__date.uni-calendar__date--multiple {
      background-color: rgba(255, 0, 67, 0.15) !important;
    }
  }

  // 如果是范围选择，还需要覆盖范围样式
  ::v-deep .uni-calendar__date--range {
    background-color: rgba(255, 0, 67, 0.1) !important;
  }

  // 强制覆盖所有可能的选中状态
  ::v-deep .uni-calendar__date[class*="checked"] {
    background-color: #FF0043 !important;
    color: #fff !important;
  }

  // 精确定位选中开始日期的样式
  ::v-deep .uni-calendar-item__weeks-box-item.uni-calendar-item--before-checked {
    background-color: #FF0043 !important;
    color: #fff !important;
  }

  // 精确定位选中结束日期的样式  
  ::v-deep .uni-calendar-item__weeks-box-item.uni-calendar-item--after-checked {
    background-color: #FF0043 !important;
    color: #fff !important;
  }

  // 选中日期之间的日期样式（更浅的背景色）
  ::v-deep .uni-calendar-item__weeks-box-item.uni-calendar-item--multiple:not(.uni-calendar-item--before-checked):not(.uni-calendar-item--after-checked) {
    background-color: transparent!important;
    color: #333 !important;
  }

  // 外层容器的背景色调整
  ::v-deep .uni-calendar-item__weeks-box.uni-calendar-item--before-checked-x {
    background-color: rgba(255, 0, 67, 0.05) !important;
  }

  ::v-deep .uni-calendar-item__weeks-box.uni-calendar-item--after-checked-x {
    background-color: rgba(255, 0, 67, 0.05) !important;
  }

  // 如果还有其他范围选择的外层容器
  ::v-deep .uni-calendar-item__weeks-box.uni-calendar-item--multiple {
    background-color: rgba(255, 0, 67, 0.05) !important;
  }

  // 范围选择的整行背景
  ::v-deep .uni-calendar__weeks-item .uni-calendar-item--hook.uni-calendar-item--multiple {
    background-color: rgba(255, 0, 67, 0.05) !important;
  }

  // 选中日期的文字样式
  ::v-deep .uni-calendar-item--checked-range-text {
    color: #fff !important;
  }
}

.status-dropdown-mask {
  position: fixed;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0,0,0,0.01);
  z-index: 100;
}

.status-dropdown {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  top: 100%;
  margin-top: 8rpx;
  min-width: 260rpx;
  background: #fff;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 24rpx 0 rgba(0,0,0,0.08);
  z-index: 101;
  padding: 12rpx 0;
}

.status-dropdown-item {
  padding: 18rpx 40rpx;
  font-size: 28rpx;
  color: #262626;
  cursor: pointer;
}

.status-dropdown-item--active {
  color: #ff0043;
  font-weight: 600;
  background: #f7f7f7;
}
</style>