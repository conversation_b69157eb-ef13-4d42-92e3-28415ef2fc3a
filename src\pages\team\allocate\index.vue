<template>
  <view class="allocate-page">
    <!-- 顶部导航栏 -->
    <view class="allocate-header">
      <view class="allocate-navbar">
        <view class="allocate-navbar-left" @click="handleBack">
          <image class="allocate-navbar-back" src="@/asset/img/team/icon_back.png" />
        </view>
        <text class="allocate-navbar-title">分配团队算力</text>
        <view class="allocate-navbar-right" @click="handlePowerDetails">
          <text class="allocate-navbar-details">算力明细</text>
        </view>
      </view>
    </view>

    <!-- 主要内容区域 -->
    <scroll-view
      class="allocate-content"
      scroll-y="true"
      show-scrollbar="false"
      enhanced="true"
      bounces="true"
      @scrolltolower="onScrollToLower"
      :scroll-top="scrollTop"
      @scroll="onScroll"
      :key="scrollViewKey"
    >
      <view class="allocate-content-inner">
        <!-- 加载状态 -->
        <view v-if="initialLoading" class="allocate-loading">
          <text class="allocate-loading-text">加载中...</text>
        </view>

        <!-- 数据加载完成后的内容 -->
        <view v-else>
                     <!-- 创建者区域 -->
           <text class="allocate-section-title">创建者</text>
           <view class="allocate-creator-section">
             <view v-if="teamMembers.filter(m => m.user_type === 1).length > 0">
               <view
                 v-for="creator in teamMembers.filter(m => m.user_type === 1)"
                 :key="creator.id"
                 class="allocate-creator-item"
               >
                 <view class="allocate-creator-avatar">
                   <image 
                     class="allocate-creator-avatar-img" 
                     :src="creator.avatar || profileAvatar" 
                     mode="aspectFill"
                   />
                 </view>
                                   <view class="allocate-creator-info">
                    <text class="allocate-creator-name">{{ creator.nickname }}</text>
                  </view>
                  <view class="allocate-creator-power">
                    <image class="allocate-creator-power-icon" src="@/asset/img/team/icon_lightning_green.svg" />
                    <text class="allocate-creator-power-value">{{ creator.power }}</text>
                  </view>
               </view>
             </view>
             <view v-else class="allocate-empty">
               <text class="allocate-empty-text">暂无创建者</text>
             </view>
           </view>

           <!-- 成员区域 -->
           <text class="allocate-section-title">成员</text>
           <view class="allocate-members-section">
             <view v-if="teamMembers.filter(m => m.user_type === 2).length > 0">
               <view
                 v-for="member in teamMembers.filter(m => m.user_type === 2)"
                 :key="member.id"
                 class="allocate-member-item"
               >
                 <view class="allocate-member-avatar">
                   <image 
                     class="allocate-member-avatar-img" 
                     :src="member.avatar || profileAvatar" 
                     mode="aspectFill"
                   />
                 </view>
                                   <view class="allocate-member-info">
                    <text class="allocate-member-name">{{ member.name || member.nickname || member.username }}</text>
                  </view>
                                     <view class="allocate-member-power-controls">
                     <view class="allocate-member-minus" @click="handleDecreasePower(member)">
                       <image class="allocate-member-minus-icon" src="@/asset/img/team/icon_minus.svg" />
                     </view>
                     <view class="allocate-member-power-value" @click="handleIncreasePower(member)">
                       <image class="allocate-member-power-icon" src="@/asset/img/team/icon_lightning_green.svg" />
                       <text>{{ member.power }}</text>
                     </view>
                     <view class="allocate-member-plus" @click="handleQuickIncrease(member)">
                       <image class="allocate-member-plus-icon" src="@/asset/img/team/icon_plus.svg" />
                     </view>
                   </view>
               </view>
             </view>
             <view v-else class="allocate-empty">
               <text class="allocate-empty-text">暂无团队成员</text>
             </view>
           </view>
          <!-- 加载中/没有更多 -->
          <view class="allocate-nomore" v-if="loading && teamMembers.length > 0">
            <text>加载中...</text>
          </view>
          <view class="allocate-nomore" v-else-if="!pagination.hasNext && teamMembers.filter(m => m.user_type === 2).length > 0">
            <text>没有更多了</text>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 算力增加弹窗 -->
    <view v-if="showPowerModal" class="allocate-power-modal">
      <view class="allocate-power-overlay" @click="closePowerModal"></view>
      <view class="allocate-power-content">
        <!-- 弹窗头部 -->
        <view class="allocate-power-header">
          <view class="allocate-power-close" @click="closePowerModal">
            <image class="allocate-power-close-icon" src="@/asset/img/team/icon_close.png" />
          </view>
          <text class="allocate-power-title">增加当前成员算力</text>
          <view class="allocate-power-placeholder"></view>
        </view>

        <!-- 算力信息 -->
        <view class="allocate-power-info">
          <view class="allocate-power-info-item">
            <text class="allocate-power-info-value">{{ teamAvailablePower }}</text>
            <text class="allocate-power-info-label">团队可用算力</text>
          </view>
          <view class="allocate-power-info-item">
            <text class="allocate-power-info-value">{{ memberAvailablePower }}</text>
            <text class="allocate-power-info-label">成员可用算力</text>
          </view>
        </view>

        <!-- 输入框 -->
        <view class="allocate-power-input">
          <input 
            class="allocate-power-input-field"
            type="number"
            v-model="powerInputValue"
            placeholder="请输入算力数值"
            @input="handlePowerInput"
          />
        </view>

        <!-- 确定按钮 -->
        <view class="allocate-power-confirm" @click="handleConfirmPower">
          <text class="allocate-power-confirm-text">确定增加</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { teamService, userService } from '@/service'
import { getCid, getUserId } from '@/utils/http'
import profileAvatar from '@/asset/img/profile/profile-header-avatar.png'


// 响应式数据
const showPowerModal = ref(false)
const powerInputValue = ref('')
const selectedMember = ref(null)
const loading = ref(false)
const initialLoading = ref(true) // 初始加载状态

// 分页与加载状态
const pagination = ref({ page: 1, size: 20, hasNext: false })
const scrollViewKey = ref(0)
const scrollTop = ref(0)

// 团队算力数据
const teamComputePowerData = ref(null)
const teamAvailablePowerValue = ref(0)

// 团队成员数据
const teamMembers = ref([])

// 计算属性
const teamAvailablePower = computed(() => {
  return teamAvailablePowerValue.value
})

const memberAvailablePower = computed(() => {
  if (!selectedMember.value) return 0
  return selectedMember.value.power
})

// 返回上一页
const handleBack = () => {
  uni.navigateBack()
}

// 算力明细
const handlePowerDetails = () => {
  uni.navigateTo({
    url: '/pages/team/power-details/index'
  })
}

// 获取团队算力信息
const fetchTeamComputePower = async () => {
  try {
    const cid = getCid()
    const userId = getUserId()

    if (!cid || !userId) {
      console.error('缺少必要参数: cid或userId')
      return
    }

    const response = await teamService.getTeamComputePower({
      cid: parseInt(cid),
      user_id: parseInt(userId)
    })

    console.log('团队算力信息:', response)

    if (response && response.status_code === 1) {
      teamComputePowerData.value = response.data
      teamAvailablePowerValue.value = response.data.available_power || 0
    } else {
      console.error('获取团队算力信息失败:', response.message)
    }
  } catch (error) {
    console.error('获取团队算力信息失败:', error)
  }
}

// 获取团队成员列表（分页）
const fetchTeamMembers = async (isLoadMore = false) => {
  if (loading.value) return
  loading.value = true
  let nextPage = isLoadMore ? pagination.value.page + 1 : 1
  const params = {
    page: nextPage,
    size: pagination.value.size
  }
  try {
    const response = await userService.getTeamMembers(params)
    if (response && response.status_code === 1) {
      const items = response.data?.items || []

      // 处理成员数据
      const processedMembers = items.map(member => ({
        id: member.id || member.user_id,
        name: member.name,
        username: member.username,
        nickname: member.nickname,
        avatar: member.avatar,
        user_type: member.user_type,
        power: member.compute_power_team ? member.compute_power_team.available_power || 0 : 0,
        computeInfo: member.compute_power_team || null,
        teamPowerInfo: member.compute_power_team || null
      }))

      if (isLoadMore) {
        teamMembers.value = teamMembers.value.concat(processedMembers)
      } else {
        teamMembers.value = processedMembers
      }
      // 分页信息
      if (response.pagination) {
        pagination.value.hasNext = response.pagination.has_next
      } else {
        pagination.value.hasNext = false
      }
      pagination.value.page = nextPage
    } else {
      if (!isLoadMore) teamMembers.value = []
      uni.showToast({ title: response?.message || '获取团队成员失败', icon: 'none' })
    }
  } catch (error) {
    if (!isLoadMore) teamMembers.value = []
    uni.showToast({ title: error?.data?.message || '网络错误', icon: 'none' })
  } finally {
    loading.value = false
  }
}

// 触底加载
const onScrollToLower = () => {
  if (pagination.value.hasNext && !loading.value) {
    fetchTeamMembers(true)
  }
}
// 滚动记录
const onScroll = (e) => {
  scrollTop.value = e.detail?.scrollTop
}

// 减少算力
const handleDecreasePower = async (member) => {
  const validation = validatePowerAllocation(member, -1)
  if (!validation.valid) {
    uni.showToast({
      title: validation.message,
      icon: 'none'
    })
    return
  }

  try {
    uni.showLoading({
      title: '回收中...',
      mask: true
    })

    // 调用回收算力接口（分配负数算力）
    const response = await teamService.allocateComputePower({
      to_user_id: parseInt(member.id),
      amount: -1, // 减少1个算力
    })

    if (response && response.status_code === 1) {
      uni.showToast({
        title: `从${member.nickname || member.name || '该成员'}成功回收1算力`,
        icon: 'none'
      })

      // 重新获取最新数据
      await Promise.all([
        fetchTeamComputePower(),
        fetchTeamMembers()
      ])
    } else {
      uni.showToast({
        title: response.message || '回收失败',
        icon: 'error'
      })
    }
  } catch (error) {
    console.error('回收算力失败:', error)
    uni.showToast({
      title: '回收失败，请重试',
      icon: 'error'
    })
  } finally {
    uni.hideLoading()
  }
}

// 快速增加算力（+1）
const handleQuickIncrease = async (member) => {
  // 检查团队是否有可用算力
  if (teamAvailablePower.value <= 0) {
    uni.showToast({
      title: '团队算力不足，无法分配',
      icon: 'none'
    })
    return
  }

  const validation = validatePowerAllocation(member, 1)
  if (!validation.valid) {
    uni.showToast({
      title: validation.message,
      icon: 'error'
    })
    return
  }

  try {
    uni.showLoading({
      title: '分配中...',
      mask: true
    })

    // 调用分配算力接口
    const response = await teamService.allocateComputePower({
      to_user_id: parseInt(member.id),
      amount: 1, // 增加1个算力
    })

    if (response && response.status_code === 1) {
      uni.showToast({
        title: `成功分配1算力给${member.nickname || member.name || '该成员'}`,
        icon: 'success'
      })

      // 重新获取最新数据
      await Promise.all([
        fetchTeamComputePower(),
        fetchTeamMembers()
      ])
    } else {
      uni.showToast({
        title: response.message || '分配失败',
        icon: 'error'
      })
    }
  } catch (error) {
    console.error('分配算力失败:', error)
    uni.showToast({
      title: '分配失败，请重试',
      icon: 'error'
    })
  } finally {
    uni.hideLoading()
  }
}

// 增加算力（弹窗输入）
const handleIncreasePower = (member) => {
  // 检查团队是否有可用算力
  if (teamAvailablePower.value <= 0) {
    uni.showToast({
      title: '团队算力不足，无法分配',
      icon: 'none'
    })
    return
  }

  selectedMember.value = member
  showPowerModal.value = true
  powerInputValue.value = ''
}

// 关闭算力弹窗
const closePowerModal = () => {
  showPowerModal.value = false
  selectedMember.value = null
  powerInputValue.value = ''
}

// 算力输入处理
const handlePowerInput = (e) => {
  powerInputValue.value = e.detail.value
}

// 确认增加算力
const handleConfirmPower = async () => {
  const powerValue = parseInt(powerInputValue.value)

  if (isNaN(powerValue) || powerValue <= 0) {
    uni.showToast({
      title: '请输入有效数值',
      icon: 'error'
    })
    return
  }

  // 验证算力分配的有效性
  const validation = validatePowerAllocation(selectedMember.value, powerValue)
  if (!validation.valid) {
    uni.showToast({
      title: validation.message,
      icon: 'error'
    })
    return
  }

  if (powerValue > teamAvailablePower.value) {
    uni.showToast({
      title: '算力不足',
      icon: 'error'
    })
    return
  }

  if (!selectedMember.value) {
    uni.showToast({
      title: '请选择成员',
      icon: 'error'
    })
    return
  }

  try {
    uni.showLoading({
      title: '分配中...',
      mask: true
    })

    // 调用分配算力接口
    const response = await teamService.allocateComputePower({
      to_user_id: parseInt(selectedMember.value.id),
      amount: powerValue,
    })

    if (response && response.status_code === 1) {
      // 分配成功，更新本地数据
      selectedMember.value.power += powerValue

      uni.showToast({
        title: '算力分配成功',
        icon: 'success'
      })

      await Promise.all([
        fetchTeamComputePower(),
        fetchTeamMembers()
      ])
    } else {
      uni.showToast({
        title: response.message || '分配失败',
        icon: 'error'
      })
    }
  } catch (error) {
    console.error('分配算力失败:', error)
    uni.showToast({
      title: '分配失败，请重试',
      icon: 'error'
    })
  } finally {
    uni.hideLoading()
    closePowerModal()
  }
}

// 页面加载时初始化
onMounted(async () => {
  try {
    await Promise.all([
      fetchTeamComputePower(),
      fetchTeamMembers()
    ])
  } finally {
    initialLoading.value = false
  }
})

// 验证算力分配的有效性
const validatePowerAllocation = (member, amount) => {
  if (!member) {
    return { valid: false, message: '请选择成员' }
  }

  if (amount > 0 && amount > teamAvailablePower.value) {
    return { valid: false, message: '团队算力不足' }
  }

  if (amount < 0 && Math.abs(amount) > member.teamPowerInfo.available_power) {
    return { valid: false, message: '成员算力不足' }
  }

  return { valid: true, message: '' }
}

</script>

<style lang="scss" scoped>
.allocate-page {
  min-height: 100vh;
  background: linear-gradient(180deg, #ffdede 0%, #f5f5f5 30%, #f9f9f9 60%, #ffffff 100%);
  position: relative;
}

// 顶部导航栏
.allocate-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  height: calc(88rpx + var(--status-bar-height));
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #ffdede;
  box-shadow: none;
}

.allocate-navbar {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0 24rpx;
  padding-top: var(--status-bar-height);
  box-sizing: border-box;
}

.allocate-navbar-left {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.allocate-navbar-back {
  width: 40rpx;
  height: 40rpx;
}

.allocate-navbar-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #000;
  flex: 1;
  text-align: center;
}

.allocate-navbar-right {
  width: 120rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.allocate-navbar-details {
  font-size: 28rpx;
  color: #666;
  font-weight: 400;
}

// 主要内容区域
.allocate-content {
  margin-top: calc(88rpx + var(--status-bar-height));
  padding: 24rpx;
  height: calc(100vh - 88rpx - var(--status-bar-height));
}

.allocate-content-inner {
  display: flex;
  flex-direction: column;
}

// 创建者区域
.allocate-creator-section {
  margin-bottom: 48rpx;
  margin-top: 16rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background: #fff;
}

.allocate-section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-top: 48rpx;
  margin-bottom: 16rpx;
  display: block;
}

.allocate-section-title:first-child {
  margin-top: 24rpx;
}

.allocate-creator-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 32rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.allocate-creator-item:last-child {
  border-bottom: none;
}

.allocate-creator-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.allocate-creator-avatar {
  margin-right: 24rpx;
  flex-shrink: 0;
  position: relative;
  display: flex;
  align-items: center;
}

.allocate-creator-avatar-img {
  width: 88rpx;
  height: 88rpx;
  border-radius: 50%;
  background: #f0f0f0;
}

.allocate-creator-name {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  word-break: break-all;
  margin-bottom: 4rpx;
}

.allocate-creator-power {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-left: 24rpx;
}

.allocate-creator-power-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
}

.allocate-creator-power-value {
  font-size: 28rpx;
  color: #28a745;
  font-weight: 500;
}

// 成员区域
.allocate-members-section {
  margin-bottom: 48rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background: #fff;
}

// 加载状态
.allocate-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  background: #fff;
  margin: 24rpx 0;
  border-radius: 12rpx;
}

.allocate-loading-text {
  font-size: 28rpx;
  color: #666;
}

// 空状态
.allocate-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
}

.allocate-empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.allocate-empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 12rpx;
}
.allocate-empty-subtext {
  font-size: 24rpx;
  color: #bbb;
  margin-bottom: 24rpx;
}
.allocate-empty-invite-btn {
  width: 220rpx;
  height: 64rpx;
  background: #000;
  color: #fff;
  border-radius: 32rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}

.allocate-member-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 32rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.allocate-member-item:last-child {
  border-bottom: none;
}

.allocate-member-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.allocate-member-avatar {
  margin-right: 24rpx;
  flex-shrink: 0;
  position: relative;
  display: flex;
  align-items: center;
}

.allocate-member-avatar-img {
  width: 88rpx;
  height: 88rpx;
  border-radius: 50%;
  background: #f0f0f0;
}

.allocate-member-name {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  word-break: break-all;
}

.allocate-member-power-controls {
  display: flex;
  flex-direction: row;
  align-items: center;
  border-radius: 6rpx;
  overflow: hidden;
  margin-left: 24rpx;
}

.allocate-member-minus,
.allocate-member-plus {
  width: 56rpx;
  height: 56rpx;
  background-color: #f8f8f8;
  display: flex;
  align-items: center;
  justify-content: center;
}

.allocate-member-plus {
  border-right: none;
}

.allocate-member-minus-icon,
.allocate-member-plus-icon {
  width: 24rpx;
  height: 24rpx;
}

.allocate-member-power-value {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0 16rpx;
  min-width: 80rpx;
  height: 56rpx;
  background-color: #fff;
  cursor: pointer;
  
  text {
    font-size: 28rpx;
    color: #28a745;
    font-weight: 500;
  }
}

.allocate-member-power-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
}

// 算力增加弹窗
.allocate-power-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 200;
  display: flex;
  align-items: center;
  justify-content: center;
}

.allocate-power-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
}

.allocate-power-content {
  position: relative;
  width: 600rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 48rpx;
  margin: 0 48rpx;
  max-height: 80vh;
  overflow-y: auto;
}

// 弹窗头部
.allocate-power-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 48rpx;
}

.allocate-power-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
  text-align: center;
}

.allocate-power-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.allocate-power-close-icon {
  width: 44rpx;
  height: 44rpx;
}

.allocate-power-placeholder {
  width: 60rpx;
  height: 60rpx;
}

// 算力信息
.allocate-power-info {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-bottom: 48rpx;
}

.allocate-power-info-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.allocate-power-info-value {
  font-size: 48rpx;
  color: #000;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.allocate-power-info-label {
  font-size: 26rpx;
  color: #666;
}

// 输入框
.allocate-power-input {
  margin-bottom: 48rpx;
}

.allocate-power-input-field {
  width: 100%;
  height: 120rpx;
  background-color: #fff;
  border: 1rpx solid #e0e0e0;
  border-radius: 16rpx;
  padding: 0 24rpx;
  font-size: 32rpx;
  color: #000;
  text-align: center;
}

// 确定按钮
.allocate-power-confirm {
  width: 100%;
  height: 88rpx;
  background-color: #4CAF50;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.allocate-power-confirm-text {
  font-size: 32rpx;
  color: #fff;
  font-weight: 600;
}
.allocate-nomore {
  font-weight: 400;
  font-size: 28rpx;
  color: #BFBFBF;
  line-height: 40rpx;
  text-align: center;
  padding: 40rpx 0;
}
</style>