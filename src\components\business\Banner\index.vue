<template>
  <view class="banner">
    <swiper 
      class="banner__swiper" 
      :indicator-dots="indicatorDots"
      :autoplay="autoplay"
      :interval="interval"
      :duration="duration"
      :circular="circular"
      indicator-color="rgba(255, 255, 255, 0.4)"
      indicator-active-color="rgba(255, 255, 255, 0.8)"
      @change="handleSwiperChange"
    >
      <swiper-item 
        v-for="(item, index) in bannerList" 
        :key="index"
        class="banner__item"
      >
        <view class="banner__content" :style="{ backgroundImage: `url(${item.backgroundImage})` }">
          <view class="banner__overlay">
            <view class="banner__text">
              <text class="banner__title">{{ item.title || $t('pages.home.bannerTitle') }}</text>
              <text class="banner__subtitle">{{ item.subtitle || $t('pages.home.bannerSubtitle') }}</text>
              <view class="banner__button interactive" @tap="handleBannerClick(item)">
                <text class="banner__button-text">{{ item.buttonText || $t('pages.home.bannerButton') }}</text>
              </view>
            </view>
            <view class="banner__image" v-if="item.image">
              <image :src="$tools.processVodImageUrl(item.image)" mode="aspectFit" class="banner__avatar"></image>
            </view>
          </view>
        </view>
      </swiper-item>
    </swiper>
    
    <!-- 自定义指示器 -->
    <view v-if="showCustomIndicator" class="banner__indicators">
      <view 
        v-for="(item, index) in bannerList" 
        :key="index"
        class="banner__indicator"
        :class="{ 'banner__indicator--active': index === currentIndex }"
        @tap="goToSlide(index)"
      ></view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
// 移除vue-i18n导入，使用uni-app官方国际化方案

// Props
const props = defineProps({
  bannerList: {
    type: Array,
    default: () => [
      {
        title: '',
        subtitle: '',
        buttonText: '',
        image: '/static/images/banner-avatar.png',
        backgroundImage: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        link: '/pages/create/index'
      }
    ]
  },
  indicatorDots: {
    type: Boolean,
    default: false
  },
  autoplay: {
    type: Boolean,
    default: true
  },
  interval: {
    type: Number,
    default: 4000
  },
  duration: {
    type: Number,
    default: 500
  },
  circular: {
    type: Boolean,
    default: true
  },
  showCustomIndicator: {
    type: Boolean,
    default: true
  }
})

// Emits
const emit = defineEmits(['click', 'change'])

// Composables  
// 注意：如果需要使用翻译功能，请从全局属性获取 $t

// State
const currentIndex = ref(0)

// Computed
const swiperRef = ref(null)

// Methods
const handleSwiperChange = (e) => {
  currentIndex.value = e.detail.current
  emit('change', e.detail.current)
}

const handleBannerClick = (item) => {
  emit('click', item)
  
  // 默认跳转行为
  if (item.link) {
    uni.navigateTo({
      url: item.link
    })
  }
}

const goToSlide = (index) => {
  currentIndex.value = index
  // 这里可以通过 swiper 实例跳转到指定 slide
  // 但 uni-app 的 swiper 组件不支持直接跳转，需要通过其他方式实现
}
</script>

<style lang="scss" scoped>
@use '@/styles/variables.scss' as *;

.banner {
  position: relative;
  height: 400rpx;
  overflow: hidden;
  border-radius: $radius-lg;
  margin: $spacing-lg;
  
  &__swiper {
    width: 100%;
    height: 100%;
  }
  
  &__item {
    width: 100%;
    height: 100%;
  }
  
  &__content {
    position: relative;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-size: cover;
    background-position: center;
    border-radius: $radius-lg;
    overflow: hidden;
  }
  
  &__overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0,0,0,0.3) 0%, rgba(0,0,0,0.1) 100%);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: $spacing-xl;
  }
  
  &__text {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
  }
  
  &__title {
    font-size: $font-size-3xl;
    font-weight: $font-weight-bold;
    color: $text-inverse;
    margin-bottom: $spacing-sm;
    line-height: $line-height-tight;
  }
  
  &__subtitle {
    font-size: $font-size-base;
    color: rgba($text-inverse, 0.9);
    margin-bottom: $spacing-lg;
    line-height: $line-height-normal;
  }
  
  &__button {
    background: $gradient-secondary;
    padding: $spacing-sm $spacing-lg;
    border-radius: $radius-full;
    box-shadow: $shadow-base;
    
    &:active {
      transform: translateY(1rpx);
      box-shadow: $shadow-sm;
    }
  }
  
  &__button-text {
    font-size: $font-size-sm;
    font-weight: $font-weight-medium;
    color: $text-inverse;
  }
  
  &__image {
    flex-shrink: 0;
    width: 240rpx;
    height: 240rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: $spacing-lg;
  }
  
  &__avatar {
    width: 200rpx;
    height: 200rpx;
    border-radius: $radius-lg;
    box-shadow: $shadow-lg;
  }
  
  &__indicators {
    position: absolute;
    bottom: $spacing-lg;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    gap: $spacing-sm;
  }
  
  &__indicator {
    width: 16rpx;
    height: 16rpx;
    border-radius: $radius-full;
    background-color: rgba($text-inverse, 0.4);
    transition: all $transition-fast;
    
    &--active {
      width: 32rpx;
      background-color: rgba($text-inverse, 0.8);
    }
  }
}

// 响应式适配
@media (max-width: 400px) {
  .banner {
    height: 320rpx;
    margin: $spacing-base;
    
    &__overlay {
      padding: $spacing-lg;
    }
    
    &__title {
      font-size: $font-size-xl;
    }
    
    &__subtitle {
      font-size: $font-size-sm;
    }
    
    &__image {
      width: 180rpx;
      height: 180rpx;
    }
    
    &__avatar {
      width: 160rpx;
      height: 160rpx;
    }
  }
}
</style> 