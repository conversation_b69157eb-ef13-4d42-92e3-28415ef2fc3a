/**
 * 测试 processVodImageUrl 方法
 */
const { processVodImageUrl } = require('./tools.js')

// 测试用例
const testCases = [
  {
    name: '正常URL，无参数，默认高度',
    url: 'https://vod.qinsilk.com/video/123.mp4',
    hSize: undefined,
    expected: 'https://vod.qinsilk.com/video/123.mp4?x-oss-process=image/auto-orient,1/resize,h_480'
  },
  {
    name: '正常URL，无参数，自定义高度',
    url: 'https://vod.qinsilk.com/video/123.mp4',
    hSize: 720,
    expected: 'https://vod.qinsilk.com/video/123.mp4?x-oss-process=image/auto-orient,1/resize,h_720'
  },
  {
    name: 'URL已有参数，追加参数',
    url: 'https://vod.qinsilk.com/video/123.mp4?param1=value1',
    hSize: 600,
    expected: 'https://vod.qinsilk.com/video/123.mp4?param1=value1&x-oss-process=image/auto-orient,1/resize,h_600'
  },
  {
    name: '非vod.qinsilk.com域名，直接返回',
    url: 'https://example.com/image.jpg',
    hSize: 480,
    expected: 'https://example.com/image.jpg'
  },
  {
    name: '空URL，直接返回',
    url: '',
    hSize: 480,
    expected: ''
  },
  {
    name: 'null URL，直接返回',
    url: null,
    hSize: 480,
    expected: null
  }
]

// 运行测试
console.log('开始测试 processVodImageUrl 方法...')

testCases.forEach((testCase, index) => {
  const result = processVodImageUrl(testCase.url, testCase.hSize)
  const passed = result === testCase.expected
  
  console.log(`测试 ${index + 1}: ${testCase.name}`)
  console.log(`  输入: url="${testCase.url}", hSize=${testCase.hSize}`)
  console.log(`  期望: "${testCase.expected}"`)
  console.log(`  实际: "${result}"`)
  console.log(`  结果: ${passed ? '✅ 通过' : '❌ 失败'}`)
  console.log('---')
})

console.log('测试完成！') 