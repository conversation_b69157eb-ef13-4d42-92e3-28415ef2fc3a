import {
	createSSRApp
} from "vue";
import App from "./App.vue";
import pinia from './store'

// 导入工具类
import tools from './utils/tools'

// 国际化配置
import en from './en.json'
import zhHans from './zh-<PERSON>.json'
import zhHant from './zh-Hant.json'
import { createI18n } from 'vue-i18n'

const messages = {
	en,
	'zh-Hans': zhHans,
	'zh-Hant': zhHant
}

let i18nConfig = {
  locale: uni.getLocale(),// 获取已设置的语言
  messages
}

const i18n = createI18n(i18nConfig)

// 导入全局样式
import './styles/common.scss'

export function createApp() {
	const app = createSSRApp(App);
	
	// 使用Pinia状态管理
	app.use(pinia)
	
	// 使用国际化
	app.use(i18n)
	
	// 注册全局工具方法
	app.config.globalProperties.$tools = tools
	
	return {
		app,
	};
}
