<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <script>
      var coverSupport = 'CSS' in window && typeof CSS.supports === 'function' && (CSS.supports('top: env(a)') ||
        CSS.supports('top: constant(a)'))
      document.write(
        '<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' +
        (coverSupport ? ', viewport-fit=cover' : '') + '" />')
    </script>
    <title></title>
    <link rel="icon" type="image/svg+xml" href="/src/asset/img/logo/logo.svg" />
    <!--preload-links-->
    <!--app-context-->
    <!-- 阿里云VOD上传SDK -->
    <script src="/src/lib/aliyun-upload-sdk-1.5.6/lib/es6-promise.min.js"></script>
    <script src="/src/lib/aliyun-upload-sdk-1.5.6/lib/aliyun-oss-sdk-6.17.1.min.js"></script>
    <script src="/src/lib/aliyun-upload-sdk-1.5.6/lib/aliyun-upload-sdk-1.5.7.min.js"></script>
  </head>
  <body>
    <div id="app"><!--app-html--></div>
    <script type="module" src="/src/main.js"></script>
  </body>
</html>
