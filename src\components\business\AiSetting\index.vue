<template>
  <!-- AI设置弹窗 -->
  <view v-if="visible" class="ai-setting-modal" @click="handleMaskClick">
    <view class="ai-setting-container" @click.stop>
      <!-- 头部 -->
      <view class="ai-setting-header">
        <image class="ai-setting-close-btn" src="@/asset/img/create/ai_setting_icon.png" @click="handleClose" />
        <text class="ai-setting-title">AI智能体设置</text>
        <view class="ai-setting-placeholder"></view>
      </view>

      <!-- 内容区域 -->
      <scroll-view class="ai-setting-content" scroll-y="true">
        <!-- AI助手提示区 -->
        <view class="ai-setting-tips">
          <view class="ai-setting-tips-content">
            <image 
              class="ai-setting-tips-icon" 
              src="@/asset/img/ai-setting/ai_setting_icon.png" 
              mode="aspectFit"
              alt="AI助手头像"
            />
            <text class="ai-setting-tips-text">主人，为了让我能为您做得更多更专业，请您完善以下信息。</text>
          </view>
        </view>

        <!-- 本店定位 -->
        <view class="ai-setting-box">
          <view class="ai-setting-section-title">
            <view class="ai-setting-title-dot"></view>
            <text class="ai-setting-title-text">本店定位</text>
          </view>
          <view class="ai-setting-item-wrapper">
            <view
              v-for="item in shopLocations"
              :key="item.id"
              class="ai-setting-item"
              :class="{ 'ai-setting-item-active': formData.shopLocationId === item.id }"
              @click="selectShopLocation(item.id)"
            >
              {{ item.name }}
            </view>
          </view>
        </view>

        <!-- 主营类型 -->
        <view class="ai-setting-box">
          <view class="ai-setting-section-title">
            <view class="ai-setting-title-dot"></view>
            <text class="ai-setting-title-text">主营类型</text>
          </view>
          <view class="ai-setting-item-wrapper">
            <view
              v-for="item in assistantIndustrys"
              :key="item.id"
              class="ai-setting-item"
              :class="{ 'ai-setting-item-active': formData.assistantIndustryIdList.includes(item.id) }"
              @click="toggleAssistantIndustry(item.id)"
            >
              {{ item.name }}
            </view>
          </view>
        </view>

        <!-- 经营类目 -->
        <view class="ai-setting-box">
          <view class="ai-setting-section-title">
            <view class="ai-setting-title-dot"></view>
            <text class="ai-setting-title-text">经营类目（大类）</text>
          </view>
          <view class="ai-setting-item-wrapper">
            <view
              v-for="item in firstCategorys"
              :key="item.id"
              class="ai-setting-item"
              :class="{ 'ai-setting-item-active': formData.firstCategoryId === item.id && item.id !== '' }"
              @click="firstCategoryClick(item)"
            >
              {{ item.name }}
            </view>
          </view>

          <!-- 经营类目（小类） -->
          <view class="ai-setting-section-title">
              <view class="ai-setting-title-dot"></view>
              <text class="ai-setting-title-text">经营类目（小类）</text>
            </view>
            <view class="ai-setting-item-wrapper">
              <view
                v-for="item in selectSecondCategorys"
                :key="item.id"
                class="ai-setting-item"
                :class="{ 'ai-setting-item-active': formData.secondCategoryIdList.includes(item.id) }"
                @click="secondCategoryClick(item)"
              >
                {{ item.name }}
              </view>
            </view>
        </view>

        <!-- 门店地址 -->
        <view class="ai-setting-box">
          <view class="ai-setting-section-title">
            <view class="ai-setting-title-dot"></view>
            <text class="ai-setting-title-text">门店地址</text>
            <view class="ai-setting-paste-btn" @click="handlePasteAddress">
              <text class="ai-setting-paste-text">粘贴并识别地址</text>
            </view>
          </view>

          <!-- 城市选择 -->
          <view class="ai-setting-city-row">
            <text class="ai-setting-city-label">选择城市</text>
            <view class="ai-setting-city-picker-wrapper">
              <CityPicker
                v-model="formData.cityArray"
                placeholder="请选择城市"
                :levels="['city', 'district']"
                @change="onCityChange"
              />
              <view class="ai-setting-clear-btn" @click="clearCity" v-if="formData.cityText">
                <text class="ai-setting-clear-text">清空</text>
              </view>
            </view>
          </view>

          <!-- 详细地址 -->
          <textarea
            class="ai-setting-address-input"
            placeholder="请填写详细地址"
            v-model="formData.comAddress"
            maxlength="50"
            :rows="2"
          />
        </view>

        <!-- 店铺大小和陈列衣杆 -->
        <view class="ai-setting-box" id="ai-setting-shopArea">
          <view class="ai-setting-shop-area-row">
            <view class="ai-setting-section-title  ai-setting-shopArea">
              <view class="ai-setting-title-dot"></view>
              <text class="ai-setting-title-text">店铺大小</text>
            </view>
            <view class="ai-setting-area-input-box">
              <input
                class="ai-setting-area-input"
                type="number"
                placeholder="请填写门店面积"
                v-model="formData.shopArea"
                @input="formData.shopArea = validateNumberInput($event.detail.value, 0, 999999)"
              />
              <text class="ai-setting-area-divider">|</text>
              <text class="ai-setting-area-unit">m²</text>
            </view>
          </view>

          <!-- 陈列衣杆长度 - 只有选中零售、连锁才显示 -->
          <view class="ai-setting-clothes-line-section">
            <view class="ai-setting-section-title">
              <view class="ai-setting-title-dot"></view>
              <text class="ai-setting-title-text">陈列衣杆长度</text>
              <text class="ai-setting-clothes-line-tip">（限制输入30CM到999CM）</text>
            </view>

            <!-- 衣杆列表 -->
            <view v-if="showClothesLineItems" class="ai-setting-clothes-line-list">
              <view
                v-for="(item, index) in clothesLineLengthList"
                :key="index"
                class="ai-setting-clothes-line-item"
              >
                <text class="ai-setting-clothes-line-label">第{{ index + 1 }}杆</text>
                <view class="ai-setting-clothes-line-input-wrapper">
                  <input
                    class="ai-setting-clothes-line-input"
                    type="number"
                    v-model="item.lengthNum"
                    placeholder="请输入长度"
                    @input="validateClothesLineInput($event.detail.value, index)"
                  />
                  <view
                    v-if="item.lengthNum !== undefined && item.lengthNum !== ''"
                    class="ai-setting-clothes-line-close"
                    @click="removeClothesLineItem(index)"
                  >
                    <text>×</text>
                  </view>
                </view>
              </view>

              <!-- 增加按钮 -->
              <view class="ai-setting-clothes-line-add" @click="addClothesLineItem">
                <view class="ai-setting-clothes-line-add-icon">
                  <text>+</text>
                </view>
                <text>增加</text>
              </view>
            </view>

            <!-- 统计信息 - 总是显示 -->
            <view class="ai-setting-clothes-line-total">
              衣杆总计：{{ clothesLineTotal }}杆
            </view>
            <view class="ai-setting-clothes-line-total">
              衣杆长度总计：{{ clothesLineLengthCalc() }}厘米
            </view>

            <!-- 展开/收起按钮 - 只有衣杆数量>3时显示 -->
            <view
              v-if="showClothesLineItemsButton"
              class="ai-setting-clothes-line-toggle"
              @click="changeClothesLineItemsShow"
            >
              <text class="ai-setting-clothes-line-toggle-icon">
                {{ showClothesLineItems ? '▲' : '▼' }}
              </text>
              <text class="ai-setting-clothes-line-toggle-text">
                {{ showClothesLineItems ? '收起' : '展开' }}
              </text>
            </view>
          </view>
        </view>

        <!-- 货品适合年龄段 -->
        <view class="ai-setting-box">
          <view class="ai-setting-section-title">
            <view class="ai-setting-title-dot"></view>
            <text class="ai-setting-title-text">货品适合年龄段</text>
          </view>
          <view class="ai-setting-item-wrapper">
            <view
              v-for="item in goodsApplyAges"
              :key="item.id"
              class="ai-setting-item ai-setting-item-ages"
              :class="{ 'ai-setting-item-active': formData.goodsApplyAgeId === item.id }"
              @click="goodsApplyAgesClick(item)"
            >
              {{ item.name }}
            </view>
          </view>
        </view>

        <!-- 货品擅长客群 -->
        <view class="ai-setting-box">
          <view class="ai-setting-section-title">
            <view class="ai-setting-title-dot"></view>
            <text class="ai-setting-title-text">货品擅长客群</text>
          </view>
          <view class="ai-setting-item-wrapper">
            <view
              v-for="item in goodsClientFeatures"
              :key="item.id"
              class="ai-setting-item"
              :class="{ 'ai-setting-item-active': formData.goodsClientFeatureIdList.includes(item.id) }"
              @click="clientFeaturesClick(item)"
            >
              {{ item.name }}
            </view>
          </view>
        </view>

        <!-- 您的角色 -->
        <view class="ai-setting-box">
          <view class="ai-setting-section-title">
            <view class="ai-setting-title-dot"></view>
            <text class="ai-setting-title-text">您的角色（当前账号）</text>
          </view>
          <view class="ai-setting-item-wrapper">
            <view
              v-for="item in roles"
              :key="item.id"
              class="ai-setting-item"
              :class="{ 'ai-setting-item-active': formData.roleId === item.id && item.id !== '' }"
              @click="comStoreRoleClick(item)"
            >
              {{ item.name }}
            </view>
          </view>

          <!-- 店铺介绍 -->
          <view class="ai-setting-section-title">
            <view class="ai-setting-title-dot"></view>
            <text class="ai-setting-title-text">您自己或店铺介绍</text>
          </view>
          <textarea
            class="ai-setting-introduce-input"
            placeholder="请简单介绍您的店铺，例如：
        我们是一家开了8年的店铺，店铺擅长全品类女装，主理人有15年的服装搭配、陈列经验，擅长为客户量身定制穿搭方案，能让更多的姐妹发挥自身身材的特点，变得更美更精致。"
            v-model="formData.introduce"
            maxlength="150"
            :rows="9"
          />
        </view>

        <!-- 底部占位 -->
        <view class="ai-setting-bottom-space"></view>
      </scroll-view>

      <!-- 底部按钮 -->
      <view class="ai-setting-footer">
        <view class="ai-setting-save-btn" @click="handleSave">
          <text class="ai-setting-save-text">保存</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 地址识别弹窗 -->
  <AddressRecognitionDialog
    v-model:visible="addressDialogVisible"
    :address-data="addressParseResult"
    @confirm="handleAddressConfirm"
    @cancel="handleAddressCancel"
  />
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import CityPicker from '../CityPicker/index.vue'
// 导入地址解析工具
import intelligenceClipBoardPaste from '@/utils/address-parse.js'
// 导入地址识别弹窗组件
import AddressRecognitionDialog from './AddressRecognitionDialog.vue'

// Props定义 - 简化为只需要visible控制
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

// 事件定义 - 确保兼容Vue 2的v-model
const emit = defineEmits(['update:visible', 'save', 'input'])

// 表单数据
const formData = ref({
  shopLocationId: 1, // 本店定位
  assistantIndustryIdList: [], // 主营类型
  firstCategoryId: '', // 大类
  secondCategoryIdList: [], // 小类
  goodsClientFeatureIdList: [], // 货品客群特点
  roleId: '', // 门店角色
  goodsApplyAgeId: '', // 货品适合年龄段
  introduce: '', // 门店介绍
  shopArea: '', // 门店大小
  comAddress: '', // 门店地址
  cityArray: [],
  cityText: ''
})

// 选项数据 - 根据UI设计稿的数据
const shopLocations = ref([
  { id: 1, name: '低端' },
  { id: 2, name: '中低端' },
  { id: 3, name: '中端' },
  { id: 4, name: '中高端' },
  { id: 5, name: '高端' }
])

const assistantIndustrys = ref([
  { id: 1, name: '零售' },
  { id: 2, name: '一批' },
  { id: 3, name: '二批' },
  { id: 4, name: '连锁' }
])

const firstCategorys = ref([
  { id: 1, name: '女装', children: [] },
  { id: 2, name: '男装', children: [] },
  { id: 3, name: '童装', children: [] },
  { id: 4, name: '内衣', children: [] },
  { id: 5, name: '鞋包', children: [] },
  { id: 6, name: '配饰', children: [] }
])

// 所有小类数据
const secondCategorys = ref([
  { id: 11, name: '连衣裙', parent: { id: 1 } },
  { id: 12, name: '上衣', parent: { id: 1 } },
  { id: 13, name: '下装', parent: { id: 1 } },
  { id: 21, name: '衬衫', parent: { id: 2 } },
  { id: 22, name: 'T恤', parent: { id: 2 } },
  { id: 23, name: '裤装', parent: { id: 2 } },
  { id: 31, name: '婴儿装', parent: { id: 3 } },
  { id: 32, name: '儿童装', parent: { id: 3 } }
])

const selectSecondCategorys = ref([])

const goodsApplyAges = ref([
  { id: 1, name: '0岁-3岁' },
  { id: 2, name: '4岁-12岁' },
  { id: 3, name: '13岁-18岁' },
  { id: 4, name: '19岁-25岁' },
  { id: 5, name: '26岁-35岁' },
  { id: 6, name: '36岁-45岁' },
  { id: 7, name: '46岁-60岁' },
  { id: 8, name: '60岁以上' }
])

const goodsClientFeatures = ref([
  { id: 1, name: '学生' },
  { id: 2, name: '上班族' },
  { id: 3, name: '宝妈' },
  { id: 4, name: '时尚达人' },
  { id: 5, name: '商务人士' },
  { id: 6, name: '居家人群' }
])

const roles = ref([
  { id: 1, name: '店主' },
  { id: 2, name: '店员' },
  { id: 3, name: '导购' },
  { id: 4, name: '搭配师' },
  { id: 5, name: '买手' }
])

// 陈列衣杆相关数据
const clothesLineLengthList = ref([
  { lengthNum: undefined }
])
const showClothesLineItems = ref(true)
const showClothesLineItemsButton = ref(undefined)

// 保存状态标记
const isSave = ref(false)

// 地址识别弹窗相关
const addressDialogVisible = ref(false)
const addressParseResult = ref({ region: '', detail: '', provinceCode: '', cityCode: '', areaCode: '' })

// 计算属性
const showSecondCategory = computed(() => {
  return formData.value.assistantIndustryIdList.some(id => id === 2 || id === 4) // 一批或连锁
})

// 衣杆总计，通过方法动态设置
const clothesLineTotal = ref(0)

// 显示卡片的条件
const showCard = (cardType) => {
  if (cardType === 2) {
    // 经营类目卡片显示条件
    return formData.value.assistantIndustryIdList.length > 0
  }
  return true
}

// 选中零售、连锁即展示陈列衣杆
const showClothesLine = () => {
  return formData.value.assistantIndustryIdList.some(id => id === 1 || id === 4)
}

// 本地存储相关方法
const getAiSettings = () => {
  try {
    const settings = uni.getStorageSync('ai_settings')
    return settings ? JSON.parse(settings) : {}
  } catch (error) {
    console.error('获取AI设置失败:', error)
    return {}
  }
}

const setAiSettings = (settings) => {
  try {
    if (settings && Object.keys(settings).length > 0) {
      uni.setStorageSync('ai_settings', JSON.stringify(settings))
    } else {
      uni.removeStorageSync('ai_settings')
    }
  } catch (error) {
    console.error('保存AI设置失败:', error)
  }
}

// 大类小类关系存储
const getCategoryRelation = () => {
  try {
    const relation = uni.getStorageSync('aiModalCategoryRelation')
    return relation ? JSON.parse(relation) : {}
  } catch (error) {
    console.error('获取大类小类关系失败:', error)
    return {}
  }
}

const saveCategoryRelation = (relationObj) => {
  try {
    uni.setStorageSync('aiModalCategoryRelation', JSON.stringify(relationObj))
    console.log('大类小类关系已保存到本地存储')
  } catch (error) {
    console.error('保存大类小类关系失败:', error)
  }
}

// 保存大类小类关系数据
const saveCategoryRelationData = () => {
  const categoryRelationObj = {}
  firstCategorys.value.forEach(item => {
    const secondCategoryIds = item.children && item.children.filter(child => child.id).map(child => child.id) || []
    if (secondCategoryIds.length > 0) {
      categoryRelationObj[item.id] = secondCategoryIds
    }
  })
  saveCategoryRelation(categoryRelationObj)
  console.log('保存大类小类关系:', categoryRelationObj)
}

// 保存自定义标签到本地存储
const saveCustomLabels = () => {
  try {
    const customLabels = {
      firstCategorys: firstCategorys.value,
      secondCategorys: secondCategorys.value,
      goodsApplyAges: goodsApplyAges.value,
      goodsClientFeatures: goodsClientFeatures.value,
      roles: roles.value
    }
    uni.setStorageSync('customLabels', JSON.stringify(customLabels))
    console.log('自定义标签已保存到本地存储:', {
      firstCategorys: firstCategorys.value.length,
      secondCategorys: secondCategorys.value.length,
      goodsApplyAges: goodsApplyAges.value.length,
      goodsClientFeatures: goodsClientFeatures.value.length,
      roles: roles.value.length
    })
  } catch (error) {
    console.error('保存自定义标签失败:', error)
  }
}

// 加载自定义标签
const loadCustomLabels = () => {
  try {
    const customLabels = uni.getStorageSync('customLabels')
    if (customLabels) {
      const labels = JSON.parse(customLabels)
      if (labels.firstCategorys) {
        firstCategorys.value = labels.firstCategorys
        console.log('加载大类数据:', labels.firstCategorys.length, '个')
      }
      if (labels.secondCategorys) {
        secondCategorys.value = labels.secondCategorys
        console.log('加载小类数据:', labels.secondCategorys.length, '个')
      }
      if (labels.goodsApplyAges) goodsApplyAges.value = labels.goodsApplyAges
      if (labels.goodsClientFeatures) goodsClientFeatures.value = labels.goodsClientFeatures
      if (labels.roles) roles.value = labels.roles
      console.log('自定义标签已从本地存储加载')
    } else {
      console.log('没有找到自定义标签数据，使用默认数据')
    }
  } catch (error) {
    console.error('加载自定义标签失败:', error)
  }
}

// 初始化大类小类关系
const initCategoryRelation = () => {
  try {
    if (firstCategorys.value.length > 0) {
      let categoryRelationObj = getCategoryRelation()

      // 如果没有保存的关系，初始化系统标签关系
      if (Object.keys(categoryRelationObj).length === 0 && secondCategorys.value.length > 0) {
        firstCategorys.value.forEach(item => {
          const parentId = item.id
          const childrenIds = secondCategorys.value.filter(child =>
            child.id && child.parent && child.parent.id === parentId
          ).map(child => child.id)
          if (childrenIds.length > 0) {
            categoryRelationObj[parentId] = childrenIds
          }
        })
        saveCategoryRelation(categoryRelationObj)
      }

      // 根据关系设置children
      firstCategorys.value.forEach(item => {
        const parentId = item.id
        const secondCategoryIds = categoryRelationObj[parentId] || []
        const children = secondCategorys.value.filter(child =>
          secondCategoryIds.includes(child.id)
        )
        item.children = children

        // 确保children中的每个项都有正确的parent关系
        item.children.forEach(child => {
          if (!child.parent) {
            child.parent = { id: parentId }
          }
        })
      })

      // 初始化选中的小类选项
      if (formData.value.firstCategoryId) {
        const selectedCategory = firstCategorys.value.find(cat => cat.id === formData.value.firstCategoryId)
        if (selectedCategory && selectedCategory.children) {
          selectSecondCategorys.value = selectedCategory.children
        }
      }

      console.log('大类小类关系初始化完成:', categoryRelationObj)
    }
  } catch (error) {
    console.error('初始化大类小类关系失败:', error)
  }
}

// 数字输入验证
const validateNumberInput = (value, min = 1, max = 9999) => {
  if (value === '' || value === undefined || value === null) return ''
  const num = parseInt(value)
  if (isNaN(num) || num < min || num > max) {
    return ''
  }
  return Math.abs(num).toString()
}

// 方法定义
const selectShopLocation = (id) => {
  formData.value.shopLocationId = id
}

const toggleAssistantIndustry = (id) => {
  const index = formData.value.assistantIndustryIdList.indexOf(id)
  if (index > -1) {
    // 最低选中一个主营类型
    if (formData.value.assistantIndustryIdList.length === 1) return
    formData.value.assistantIndustryIdList.splice(index, 1)
  } else {
    formData.value.assistantIndustryIdList.push(id)
  }

  // 如果不显示小类，清空小类选择
  if (!showSecondCategory.value) {
    formData.value.secondCategoryIdList = []
  }
}

// 新增的方法，替换原来的方法名
const firstCategoryClick = (item) => {
  try {
    if (formData.value.firstCategoryId === item.id) return
    formData.value.firstCategoryId = item.id
    formData.value.secondCategoryIdList = []

    // 更新小类选项，确保children存在
    const selectedCategory = firstCategorys.value.find(cat => cat.id === item.id)
    if (selectedCategory) {
      // 如果children不存在，从关系数据中恢复
      if (!selectedCategory.children) {
        const categoryRelationObj = getCategoryRelation()
        const secondCategoryIds = categoryRelationObj[selectedCategory.id] || []
        selectedCategory.children = secondCategorys.value.filter(child =>
          secondCategoryIds.includes(child.id)
        )
        console.log(`为大类 ${selectedCategory.name} 恢复了 ${selectedCategory.children.length} 个小类`)
      }
      selectSecondCategorys.value = selectedCategory.children
    } else {
      selectSecondCategorys.value = []
    }
  } catch (error) {
    console.error('firstCategoryClick error:', error)
    uni.showToast({
      title: '操作失败，请重试',
      icon: 'none',
      duration: 2000
    })
  }
}

const secondCategoryClick = (item) => {
  try {
    const index = formData.value.secondCategoryIdList.indexOf(item.id)
    if (index > -1) {
      formData.value.secondCategoryIdList.splice(index, 1)
    } else {
      formData.value.secondCategoryIdList.push(item.id)
    }
  } catch (error) {
    console.error('secondCategoryClick error:', error)
    uni.showToast({
      title: '操作失败，请重试',
      icon: 'none',
      duration: 2000
    })
  }
}

const goodsApplyAgesClick = (item) => {
  try {
    if (formData.value.goodsApplyAgeId === item.id) {
      formData.value.goodsApplyAgeId = ''
    } else {
      formData.value.goodsApplyAgeId = item.id
    }
  } catch (error) {
    console.error('goodsApplyAgesClick error:', error)
    uni.showToast({
      title: '操作失败，请重试',
      icon: 'none',
      duration: 2000
    })
  }
}

const clientFeaturesClick = (item) => {
  try {
    // 单选模式：如果点击的是已选中的项，则取消选择；否则选择新项
    if (formData.value.goodsClientFeatureIdList.includes(item.id)) {
      formData.value.goodsClientFeatureIdList = []
    } else {
      formData.value.goodsClientFeatureIdList = [item.id]
    }
  } catch (error) {
    console.error('clientFeaturesClick error:', error)
    uni.showToast({
      title: '操作失败，请重试',
      icon: 'none',
      duration: 2000
    })
  }
}

const comStoreRoleClick = (item) => {
  try {
    if (formData.value.roleId === item.id) {
      formData.value.roleId = ''
    } else {
      formData.value.roleId = item.id
    }
  } catch (error) {
    console.error('comStoreRoleClick error:', error)
    uni.showToast({
      title: '操作失败，请重试',
      icon: 'none',
      duration: 2000
    })
  }
}

// 陈列衣杆相关方法
const clothesLineLengthCalc = () => {
  let lengthNum = 0
  let clothesLineTotalCount = 0
  if (clothesLineLengthList.value && clothesLineLengthList.value.length > 0) {
    for (let i = 0; i < clothesLineLengthList.value.length; i++) {
      const length = clothesLineLengthList.value[i].lengthNum
      if (length && !isNaN(length)) {
        lengthNum = lengthNum + parseInt(length)
        clothesLineTotalCount++
      }
    }
  }
  // 每次计算长度时都计算衣杆数量
  clothesLineTotal.value = clothesLineTotalCount
  return lengthNum
}

const addClothesLineItem = () => {
  clothesLineLengthList.value.push({ lengthNum: undefined })
  // 添加衣杆时不动态更新showClothesLineItemsButton
}

const removeClothesLineItem = (index) => {
  // 直接设置为undefined，不删除数组项
  clothesLineLengthList.value[index].lengthNum = undefined
}

const changeClothesLineItemsShow = () => {
  showClothesLineItems.value = !showClothesLineItems.value
}

// 衣杆输入验证，直接绑定不做验证
const validateClothesLineInput = (value, index) => {
  // input直接绑定，不做实时验证，验证在保存时进行
  clothesLineLengthList.value[index].lengthNum = value
}

const onCityChange = (e) => {
  console.log(e)
  formData.value.cityArray = e.value
  formData.value.cityText = e.text
}

const clearCity = () => {
  formData.value.cityArray = []
  formData.value.cityText = ''
  uni.showToast({
    title: '城市已清空',
    icon: 'success',
    duration: 1000
  })
}

const handlePasteAddress = async () => {
  try {
    // 使用地址解析工具获取剪贴板内容并解析
    const result = await intelligenceClipBoardPaste.get()

    console.log('resutl', result)
    
    if (result && (result.province || result.city || result.area || result.detail)) {
      // 构建地区信息
      const region = [result.province, result.city, result.area]
        .filter(item => item)
        .join('-')
      
      // 设置解析结果，包含地区代码
      addressParseResult.value = {
        region: region,
        detail: result.detail || '',
        provinceCode: result.provinceCode || '',
        cityCode: result.cityCode || '',
        areaCode: result.areaCode || ''
      }
      
      // 显示地址识别弹窗
      showAddressDialog()
    } else {
      uni.showToast({
        title: '未能识别到有效地址',
        icon: 'none',
        duration: 1500
      })
    }
  } catch (error) {
    console.error('地址解析失败:', error)
    uni.showToast({
      title: '地址解析失败',
      icon: 'none',
      duration: 1500
    })
  }
}

const handleClose = () => {
  try {
    console.log('handleClose called, isSave:', isSave.value)

    // 只有在未保存时才检查是否有修改
    if (!isSave.value) {
      const hasChanges = checkForChanges()
      console.log('hasChanges:', hasChanges)

      if (hasChanges) {
        uni.showModal({
          title: '提示',
          content: '是否保存已更新的内容?',
          confirmText: '确认',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              handleSave()
            } else {
              console.log('用户选择不保存，关闭弹窗')
              emit('update:visible', false)
            }
          }
        })
        return
      }
    }

    console.log('直接关闭弹窗')
    emit('update:visible', false)
  } catch (error) {
    console.error('handleClose error:', error)
    // 出错时直接关闭弹窗
    emit('update:visible', false)
  }
}

const handleMaskClick = () => {
  handleClose()
}

// 检查是否有修改
const checkForChanges = () => {
  try {
    // 获取保存的数据
    const originalData = getAiSettings()

    // 安全的数组比较函数
    const compareArrays = (arr1, arr2) => {
      const a1 = (arr1 || []).slice().sort()
      const a2 = (arr2 || []).slice().sort()
      return JSON.stringify(a1) === JSON.stringify(a2)
    }

    // 对比主营类型、门店地址、货品使用年龄段、货盘客群特点、店铺大小、当前账号角色、店铺介绍是否有变化
    if (!compareArrays(formData.value.assistantIndustryIdList, originalData.assistantIndustryIdList) ||
        formData.value.comAddress !== (originalData.comAddress || '') ||
        !compareArrays(formData.value.goodsClientFeatureIdList, originalData.goodsClientFeatureIdList) ||
        !compareArrays(formData.value.secondCategoryIdList, originalData.secondCategoryIdList) ||
        formData.value.firstCategoryId !== (originalData.firstCategoryId || '') ||
        formData.value.shopArea !== (originalData.shopArea || '') ||
        formData.value.roleId !== (originalData.roleId || '') ||
        formData.value.introduce !== (originalData.introduce || '') ||
        formData.value.goodsApplyAgeId !== (originalData.goodsApplyAgeId || '') ||
        formData.value.shopLocationId !== (originalData.shopLocationId || 1)
    ) {
      return true
    }

    // 对比衣杆长度是否一致
    if (clothesLineLengthList.value.length > 0) {
      for (let i = 0; i < clothesLineLengthList.value.length; i++) {
        const currentLength = clothesLineLengthList.value[i].lengthNum
        const originalLength = originalData.clothesLineLengthList && originalData.clothesLineLengthList[i]
        if (currentLength !== originalLength) {
          return true
        }
      }
    }

    // 对比城市编码是否一致
    if (!compareArrays(formData.value.cityArray, originalData.cityArray)) {
      return true
    }

    return false
  } catch (error) {
    console.error('检查修改状态时出错:', error)
    // 出错时直接允许关闭，不阻塞用户操作
    return false
  }
}

const handleSave = () => {
  // 验证表单数据
  let errorMsg = ''
  
  if (typeof formData.value.shopLocationId !== 'number') {
    errorMsg = '请选择本店定位'
  } else if (formData.value.assistantIndustryIdList.length === 0) {
    errorMsg = '请选择主营类型'
  } else if (typeof formData.value.firstCategoryId !== 'number') {
    errorMsg = '请选择经营类目（大类）'
  }
  
  if (errorMsg) {
    uni.showToast({
      title: errorMsg,
      icon: 'none',
      duration: 2000
    })
    return
  }

  // 验证衣杆长度
  const validClothesLineLengthList = []
  if (clothesLineLengthList.value.length > 0) {
    for (let i = 0; i < clothesLineLengthList.value.length; i++) {
      const lineNum = clothesLineLengthList.value[i].lengthNum
      if (lineNum && lineNum !== 0 && lineNum !== '' && !isNaN(lineNum) && lineNum >= 30 && lineNum <= 999) {
        validClothesLineLengthList.push(parseInt(lineNum))
      } else if (lineNum !== undefined) {
        // 如果元素中的lengthNum为空或者不符合要求，则弹出提示框
        uni.showToast({
          title: '请输入衣杆正确的长度！',
          icon: 'none',
          duration: 2000
        })
        clothesLineLengthList.value[i].lengthNum = undefined
        return
      }
    }
  }

  // 显示保存中状态
  uni.showLoading({
    title: '保存中...',
    mask: true
  })

  // 准备保存的数据
  const saveData = {
    ...formData.value,
    clothesLineLengthList: validClothesLineLengthList,
    assistantIndustryId: 0, // 固定传0，不再使用
    cityCodeList: formData.value.cityArray || [] // 城市编码
  }

  // 模拟保存延迟
  setTimeout(() => {
    // 保存到本地存储
    setAiSettings(saveData)

    // 保存大类小类关系
    saveCategoryRelationData()

    // 设置保存状态
    isSave.value = true

    // 发送保存事件
    emit('save', saveData)

    // 隐藏加载状态
    uni.hideLoading()

    // 显示保存成功提示
    uni.showToast({
      title: '保存成功',
      icon: 'success',
      duration: 1500
    })

    // 保存后关闭弹窗
    setTimeout(() => {
      emit('update:visible', false)
    }, 1500)
  }, 500)
}

// 地址识别弹窗相关方法
const showAddressDialog = () => {
  addressDialogVisible.value = true
}

const handleAddressConfirm = (addressData) => {
  formData.value.comAddress = addressData.detail
  
  // 将地区代码传递给formData.cityArray
  if (addressData.provinceCode || addressData.cityCode || addressData.areaCode) {
    formData.value.cityArray = [
      addressData.provinceCode || '',
      addressData.cityCode || '',
      addressData.areaCode || ''
    ]
  }
  
  addressParseResult.value = { region: '', detail: '', provinceCode: '', cityCode: '', areaCode: '' }
  uni.showToast({
    title: '地址已更新',
    icon: 'success',
    duration: 1500
  })
}

const handleAddressCancel = () => {
  addressParseResult.value = { region: '', detail: '', provinceCode: '', cityCode: '', areaCode: '' }
}

// 监听弹窗显示状态，当弹窗显示时加载配置
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    loadAiSettings()
  }
})

// 加载AI设置配置
const loadAiSettings = () => {
  try {
    console.log('AiSetting loadAiSettings called')
    // 重置保存状态
    isSave.value = false

    // 先加载自定义标签
    loadCustomLabels()

    const savedSettings = getAiSettings()
    if (savedSettings && Object.keys(savedSettings).length > 0) {
      // 恢复表单数据
      Object.assign(formData.value, savedSettings)

      // 恢复衣杆数据
      if (savedSettings.clothesLineLengthList && Array.isArray(savedSettings.clothesLineLengthList) && savedSettings.clothesLineLengthList.length > 0) {
        clothesLineLengthList.value = savedSettings.clothesLineLengthList.map(item => ({
          lengthNum: item
        }))
      } else {
        clothesLineLengthList.value = [{ lengthNum: undefined }]
      }

      // 初始数据的长度列表大于3的时候，显示收起按钮
      if (clothesLineLengthList.value.length > 3) {
        showClothesLineItemsButton.value = true
        // 默认收起
        showClothesLineItems.value = false
      } else {
        // 默认展开，不设置showClothesLineItemsButton（保持undefined状态）
        showClothesLineItems.value = true

      }

      // 恢复小类选项（如果有大类选择）
      if (formData.value.firstCategoryId) {
        const selectedCategory = firstCategorys.value.find(cat => cat.id === formData.value.firstCategoryId)
        if (selectedCategory && selectedCategory.children) {
          selectSecondCategorys.value = selectedCategory.children
        }
      }

      // 确保数组字段存在
      if (!Array.isArray(formData.value.assistantIndustryIdList)) {
        formData.value.assistantIndustryIdList = []
      }
      if (!Array.isArray(formData.value.secondCategoryIdList)) {
        formData.value.secondCategoryIdList = []
      }
      if (!Array.isArray(formData.value.goodsClientFeatureIdList)) {
        formData.value.goodsClientFeatureIdList = []
      }
      if (!Array.isArray(formData.value.cityArray)) {
        formData.value.cityArray = []
      }

      console.log('AI设置数据已恢复:', formData.value)
    } else {
      // 没有保存的数据时，初始化衣杆数据
      clothesLineLengthList.value = [{ lengthNum: undefined }]
      showClothesLineItems.value = true

    }

    // 初始化大类小类关系
    initCategoryRelation()
  } catch (error) {
    console.error('AiSetting loadAiSettings error:', error)
  }
}
</script>

<style scoped>
/* AI设置弹窗样式 - 深色主题适配 */
.ai-setting-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 99;
  display: flex;
  align-items: flex-end;
}

.ai-setting-container {
  width: 100%;
  height: 100vh;
  background-color: #111113;
  display: flex;
  flex-direction: column;
}

/* 头部样式 */
.ai-setting-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--status-bar-height) 32rpx 0 32rpx;
  background-color: #212124;
  border-bottom: 2rpx solid #353537;
  color: #ffffff;
  height: calc(100rpx + var(--status-bar-height));
}

.ai-setting-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #ffffff;
  flex: 1;
  text-align: center;
}

.ai-setting-close-btn {
  width: 16rpx;
  height: 30rpx;
  display: block;
  cursor: pointer;
}

.ai-setting-placeholder {
  width: 16rpx;
  height: 30rpx;
}

/* 内容区域 */
.ai-setting-content {
  flex: 1;
  padding: 0 32rpx;
  overflow-y: auto;
  background-color: #111113;
}

/* AI助手提示区 */
.ai-setting-tips {
  padding: 24rpx 0;
  position: relative;
}

.ai-setting-tips-content {
  display: flex;
  align-items: center;
  gap: 30rpx;
  padding: 24rpx 0;
  background: transparent;
  margin-top: 0;
  padding-bottom: 0;
  border-radius: 0;
}

.ai-setting-tips-icon {
  width: 222rpx;
  height: 188rpx;
  flex-shrink: 0;
  object-fit: contain;
}

.ai-setting-tips-text {
  flex: 1;
  font-weight: 500;
  font-size: 30rpx;
  color: #FFFFFF;
  line-height: 52rpx;
  height: 188rpx;
  display: flex;
  align-items: center;
}

/* 配置盒子 */
.ai-setting-box {
  padding: 24rpx 24rpx;
  background-color: #232325;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
}

/* 标题样式 */
.ai-setting-section-title {
  display: flex;
  align-items: center;
  gap: 14rpx;
  margin-bottom: 20rpx;
  justify-content: space-between;
}

.ai-setting-shopArea {
  margin-top: 20rpx;
}

.ai-setting-title-dot {
  display: inline-block;
  width: 4rpx;
  height: 26rpx;
  background: #ff0043;
  border-radius: 2rpx;
}

.ai-setting-title-text {
  font-size: 24rpx;
  color: #ffffff;
  flex: 1;
}

/* 选项样式 */
.ai-setting-item-wrapper {
  display: flex;
  flex-wrap: wrap;
  margin-left: -20rpx;
}

.ai-setting-item {
  min-width: calc(25% - 20rpx);
  height: 64rpx;
  display: inline-block;
  padding: 12rpx;
  border-radius: 12rpx;
  background: #383839;
  text-align: center;
  margin-left: 20rpx;
  margin-bottom: 20rpx;
  color: #bfbfbf;
  font-size: 24rpx;
  line-height: 40rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;

  border: 2rpx solid transparent;
}

.ai-setting-item-ages {
  min-width: calc(33% - 20rpx);
}

.ai-setting-item-active {
  font-weight: 500;
  background: #383839;
  color: #ff0043;
  border: 2rpx solid #ff0043;
}

.ai-setting-add-item {
  border: 2rpx solid #353537;
  background-color: #383839;
  color: #bfbfbf;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4rpx;
  cursor: pointer;

}

.ai-setting-add-item:active {
  background-color: #ff0043;
  color: #ffffff;
  border-color: #ff0043;
  transform: none;
}

.ai-setting-add-icon {
  font-size: 24rpx;
  color: #bfbfbf;
}

/* 门店地址样式 */
.ai-setting-paste-btn {
  height: 44rpx;
  border-radius: 26rpx;
  border: 2rpx solid #ff0043;
  color: #ff0043;
  text-align: center;
  padding: 0 20rpx;
  margin-left: 16rpx;
  cursor: pointer;

}

.ai-setting-paste-btn:active {
  background-color: #ff0043;
  color: #ffffff;
  transform: none;
}

.ai-setting-paste-text {
  font-size: 24rpx;
  color: #ff0043;
}

.ai-setting-city-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #262626;
  font-size: 24rpx;
  margin-bottom: 24rpx;
}

.ai-setting-city-label {
  font-size: 24rpx;
  color: #ffffff;
}

.ai-setting-city-picker-wrapper {
  flex: 1;
  text-align: right;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.ai-setting-city-picker {
  border: none;
  padding: 8rpx 20rpx 8rpx 0;
  text-align: right;
}

.ai-setting-city-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.ai-setting-city-text {
  font-size: 24rpx;
  color: #ffffff;
  padding-right: 36rpx;
}

.ai-setting-city-placeholder {
  color: #8c8c8c;
}

.ai-setting-city-arrow {
  font-size: 20rpx;
  color: #999;
}

.ai-setting-clear-btn {
  height: 44rpx;
  border-radius: 26rpx;
  border: 2rpx solid #353537;
  background-color: #383839;
  color: #bfbfbf;
  text-align: center;
  padding: 0 20rpx;
  margin-left: 16rpx;
  cursor: pointer;

}

.ai-setting-clear-btn:active {
  background-color: #ff0043;
  color: #ffffff;
  transform: none;
}

.ai-setting-clear-text {
  font-size: 24rpx;
  color: #bfbfbf;
  white-space: nowrap;
}

/* 输入框样式 */
.ai-setting-address-input,
.ai-setting-introduce-input {
  width: 100%;
  background: #383839;
  border-radius: 12rpx;
  padding: 24rpx 30rpx;
  border: none;
  font-size: 24rpx;
  color: #ffffff;
  resize: none;

}

.ai-setting-address-input:focus,
.ai-setting-introduce-input:focus {
  background: #2a2a2c;
  outline: none;
}

.ai-setting-address-input::placeholder,
.ai-setting-introduce-input::placeholder {
  color: #8c8c8c;
  font-size: 24rpx;
}

.ai-setting-address-input {
  min-height: 120rpx;
  max-height: 160rpx;
}

.ai-setting-introduce-input {
  min-height: 240rpx;
}

/* 店铺大小样式 */
.ai-setting-shop-area-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 40rpx;
}

.ai-setting-area-input-box {
  display: flex;
  align-items: center;
  min-width: 228rpx;
  height: 72rpx;
  background: #383839;
  border-radius: 12rpx;
  padding: 0 20rpx;
  margin-left: 44rpx;
  flex: 1;

}

.ai-setting-area-input-box:focus-within {
  background: #2a2a2c;
}

.ai-setting-area-input {
  flex: 1;
  height: 100%;
  background: transparent;
  border: none;
  text-align: center;
  color: #ffffff;
  font-size: 28rpx;
  font-weight: 500;
  outline: none;
}

.ai-setting-area-input .input-placeholder {
  color: #8c8c8c;
  font-size: 24rpx;
  text-align: center;
}

.ai-setting-area-divider {
  color: #e5e5e5;
  margin: 0 16rpx;
  font-size: 24rpx;
}

.ai-setting-area-unit {
  color: #bfbfbf;
  font-size: 24rpx;
}

/* 陈列衣杆样式 */
.ai-setting-clothes-line-section {
  margin-top: 40rpx;
}

.ai-setting-clothes-line-tip {
  color: #8c8c8c;
  font-size: 24rpx;
  margin-left: 16rpx;
}

.ai-setting-clothes-line-list {
  margin-top: 20rpx;
}

.ai-setting-clothes-line-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  gap: 20rpx;
}

.ai-setting-clothes-line-label {
  font-size: 24rpx;
  color: #ffffff;
  min-width: 100rpx;
}

.ai-setting-clothes-line-input-wrapper {
  position: relative;
  flex: 1;
  display: flex;
  align-items: center;
}

.ai-setting-clothes-line-input {
  width: 100%;
  height: 64rpx;
  background: #383839;
  border-radius: 12rpx;
  padding: 12rpx 60rpx 12rpx 24rpx;
  border: none;
  font-size: 24rpx;
  color: #ffffff;
  outline: none;

}

.ai-setting-clothes-line-input:focus {
  background: #2a2a2c;
}

.ai-setting-clothes-line-input::placeholder {
  color: #8c8c8c;
  font-size: 24rpx;
}

.ai-setting-clothes-line-close {
  position: absolute;
  right: 16rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 32rpx;
  height: 32rpx;
  background: #ff0043;
  border-radius: 50%;
  font-size: 20rpx;
  color: #ffffff;
  cursor: pointer;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

.ai-setting-clothes-line-close text {
  display: block;
}


.ai-setting-clothes-line-add {
  width: 100%;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: none;
  color: #8C8C8C;
  font-size: 28rpx;
  cursor: pointer;

  gap: 12rpx;
}

.ai-setting-clothes-line-add-icon {
  color: #BFBFBF;
  font-size: 40rpx;
  margin-right: 12rpx;
  width: 40rpx;
  height: 40rpx;
  background: #f7f7f7;
  border-radius: 50%;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

.ai-setting-clothes-line-add-icon text {
  display: block;
  transform: translateY(-2rpx);
}

.ai-setting-clothes-line-total {
  font-family: PingFangSC-Regular, sans-serif;
  font-size: 24rpx;
  color: #bfbfbf;
  font-weight: 400;
  margin-top: 16rpx;
  display: flex;
  align-items: center;
}

.ai-setting-clothes-line-toggle {
  display: flex;
  height: 64rpx;
  width: 100%;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  margin-top: 16rpx;
  background: #383839;
  border: 2rpx solid #353537;
  border-radius: 8rpx;
  color: #bfbfbf;
  font-size: 24rpx;
  cursor: pointer;

}




.ai-setting-clothes-line-toggle-icon {
  color: #BFBFBF;
  font-size: 12px;
}

/* 底部区域 */
.ai-setting-bottom-space {
  height: 120rpx;
}

.ai-setting-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 40rpx;
  background-color: #111113;
  border-top: 2rpx solid #353537;
  z-index: 10;
}

.ai-setting-save-btn {
  width: 100%;
  height: 90rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #ff0043;
  border-radius: 48rpx;
  border: none;
  transition: all 0.2s ease;
  cursor: pointer;
}

.ai-setting-save-btn:active {
  background: #e6003a;
}

.ai-setting-save-text {
  font-size: 32rpx;
  font-weight: 500;
  color: #ffffff;
}

/* 深色主题优化 */
.ai-setting-modal {
  backdrop-filter: blur(10rpx);
}

.ai-setting-container {
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.3);
}

.ai-setting-box {
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}



/* 滚动条样式 */
.ai-setting-content::-webkit-scrollbar {
  width: 8rpx;
}

.ai-setting-content::-webkit-scrollbar-track {
  background: transparent;
}

.ai-setting-content::-webkit-scrollbar-thumb {
  background: #353537;
  border-radius: 4rpx;
}

.ai-setting-content::-webkit-scrollbar-thumb:hover {
  background: #4a4a4c;
}
</style>
